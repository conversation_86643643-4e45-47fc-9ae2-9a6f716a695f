/**======================================================================
=========================================================================
Template Name: Able Pro - Bootstrap Admin Template
Author: Phoenixcoded
Support: https://phoenixcoded.authordesk.app
File: style.css
=========================================================================
=================================================================================== */

// main framework
@import 'node_modules/bootstrap/scss/functions';
@import 'node_modules/bootstrap/scss/variables';
@import 'settings/color-variables';
@import 'settings/bootstrap-variables';
@import 'settings/theme-variables';

@import 'node_modules/bootstrap/scss/mixins';

section {
  padding: 100px 0;
}

.title {
  margin-bottom: 50px;

  h2 {
    font-weight: 600;
  }

  h5 {
    font-weight: 500;
  }
}

.landing-page {
  overflow-x: hidden;
  background: var(--bs-body-bg);

  @media (min-width: 1600px) {
    .container {
      max-width: 1200px;
    }
  }
}

.navbar {
  position: fixed;
  padding: 16px 0;
  width: 100%;
  z-index: 1099;
  top: 0;
  backdrop-filter: blur(7px);
  background-color: var(--pc-header-background);

  &.top-nav-collapse.default {
    box-shadow: none;
  }

  &.default,
  &.top-nav-collapse {
    box-shadow: 0 8px 6px -10px rgba(0, 0, 0, 0.5);
  }

  .nav-link {
    font-weight: 500;

    &:active,
    &:hover,
    &:focus {
      color: var(--bs-primary);
    }
  }
}

header {
  overflow: hidden;
  position: relative;
  padding: 100px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-size: cover;
  flex-direction: column;

  h1 {
    font-size: 55px;
  }

  .head-rating-block {
    position: relative;

    &:after {
      content: '';
      position: absolute;
      top: 20%;
      bottom: 20%;
      left: auto;
      right: 0;
      width: 1px;
      background: var(--bs-border-color);
    }
  }

  .container {
    position: relative;
    z-index: 5;
  }
}
.hero-text-gradient {
  --bg-size: 400%;
  --color-one: rgb(37, 161, 244);
  --color-two: rgb(249, 31, 169);

  background: linear-gradient(
      90deg,
      var(--color-one),
      var(--color-two),
      var(--color-one)
    )
    0 0 / var(--bg-size) 100%;
  color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  animation: move-bg 24s infinite linear;
}
@keyframes move-bg {
  to {
    background-position: var(--bg-size) 0;
  }
}

@media (max-width: 991.98px) {
  section {
    padding: 40px 0;
  }
}

@media (max-width: 767.98px) {

  header {
    text-align: center;
    padding: 100px 0;
    h1{
      font-size: 25px;
    }
  }
}