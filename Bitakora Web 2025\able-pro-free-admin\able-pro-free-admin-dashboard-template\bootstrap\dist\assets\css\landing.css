/**======================================================================
=========================================================================
Template Name: Able Pro - Bootstrap Admin Template
Author: Phoenixcoded
Support: https://phoenixcoded.authordesk.app
File: style.css
=========================================================================
=================================================================================== */
body {
  font-feature-settings: "salt";
}

h1,
h2 {
  font-weight: 700;
}

/* $btn-border-radius: 12px;
$btn-border-radius-sm: 8px;
$btn-border-radius-lg: 14px; */
:root {
  --bs-body-bg: #f8f9fa;
  --bs-body-bg-rgb: 248, 249, 250;
  --pc-heading-color: #1d2630;
  --pc-active-background: #f3f5f7;
  --pc-sidebar-background: transparent;
  --pc-sidebar-color: #5b6b79;
  --pc-sidebar-color-rgb: 91, 107, 121;
  --pc-sidebar-active-color: #0d6efd;
  --pc-sidebar-shadow: none;
  --pc-sidebar-caption-color: #3e4853;
  --pc-sidebar-border: 1px dashed #bec8d0;
  --pc-sidebar-user-background: #f3f5f7;
  --pc-header-background: rgba(var(--bs-body-bg-rgb), 0.7);
  --pc-header-color: #5b6b79;
  --pc-header-shadow: none;
  --pc-card-box-shadow: none;
  --pc-header-submenu-background: #ffffff;
  --pc-header-submenu-color: #5b6b79;
}

[data-pc-theme_contrast=true] {
  --bs-body-bg: #ffffff;
  --pc-sidebar-background: transparent;
  --pc-sidebar-active-color: #0d6efd;
  --pc-sidebar-shadow: 1px 0 3px 0px #dbe0e5;
  --pc-sidebar-border: none;
  --pc-card-box-shadow: 0px 8px 24px rgba(27, 46, 94, 0.08);
}

section {
  padding: 100px 0;
}

.title {
  margin-bottom: 50px;
}
.title h2 {
  font-weight: 600;
}
.title h5 {
  font-weight: 500;
}

.landing-page {
  overflow-x: hidden;
  background: var(--bs-body-bg);
}
@media (min-width: 1600px) {
  .landing-page .container {
    max-width: 1200px;
  }
}

.navbar {
  position: fixed;
  padding: 16px 0;
  width: 100%;
  z-index: 1099;
  top: 0;
  -webkit-backdrop-filter: blur(7px);
          backdrop-filter: blur(7px);
  background-color: var(--pc-header-background);
}
.navbar.top-nav-collapse.default {
  box-shadow: none;
}
.navbar.default, .navbar.top-nav-collapse {
  box-shadow: 0 8px 6px -10px rgba(0, 0, 0, 0.5);
}
.navbar .nav-link {
  font-weight: 500;
}
.navbar .nav-link:active, .navbar .nav-link:hover, .navbar .nav-link:focus {
  color: var(--bs-primary);
}

header {
  overflow: hidden;
  position: relative;
  padding: 100px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-size: cover;
  flex-direction: column;
}
header h1 {
  font-size: 55px;
}
header .head-rating-block {
  position: relative;
}
header .head-rating-block:after {
  content: "";
  position: absolute;
  top: 20%;
  bottom: 20%;
  left: auto;
  right: 0;
  width: 1px;
  background: var(--bs-border-color);
}
header .container {
  position: relative;
  z-index: 5;
}

.hero-text-gradient {
  --bg-size: 400%;
  --color-one: rgb(37, 161, 244);
  --color-two: rgb(249, 31, 169);
  background: linear-gradient(90deg, var(--color-one), var(--color-two), var(--color-one)) 0 0/var(--bg-size) 100%;
  color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  animation: move-bg 24s infinite linear;
}

@keyframes move-bg {
  to {
    background-position: var(--bg-size) 0;
  }
}
@media (max-width: 991.98px) {
  section {
    padding: 40px 0;
  }
}
@media (max-width: 767.98px) {
  header {
    text-align: center;
    padding: 100px 0;
  }
  header h1 {
    font-size: 25px;
  }
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
