<div class="auth-main">
  <div class="auth-wrapper version-1">
    <div class="auth-form">
      <div class="auth-background">
        <div class="yellow-circle"></div>
        <div class="red-circle"></div>
      </div>
      <div class="main-form">
        <app-card [showHeader]="false" class="auth-form-card" [padding]="40">
          <div class="text-center">
            <a href="javascript:">
              <img src="assets/images/logo-dark.svg" alt="logo" />
            </a>
            <div class="login-with">
              @for (list of loginType; track list) {
                <button mat-stroked-button class="m-t-10 text-muted">
                  <div class="flex">
                    <img src="{{ list.image }}" alt="{{ list.alt }}" />
                    <span class="mat-body p-l-10">{{ list.title }}</span>
                  </div>
                </button>
              }
            </div>
          </div>
          <div class="separator">
            <span>OR</span>
          </div>
          <h4 class="text-center m-b-25 f-20 f-w-500">Login With Your Email</h4>
          <div class="text-start">
            <label for="name" class="f-w-500">Enter Your Email</label>
            <mat-form-field appearance="outline" class="w-100 m-t-5">
              <input matInput [formControl]="email" required [(ngModel)]="Email" />
              @if (email.invalid) {
                <mat-error>{{ getErrorMessage() }}</mat-error>
              }
            </mat-form-field>
            <label for="name" class="f-w-500">Enter your password</label>
            <mat-form-field appearance="outline" class="w-100 m-t-5">
              <input matInput [type]="hide ? 'password' : 'text'" [(ngModel)]="password" />
              <button mat-icon-button matSuffix (click)="hide = !hide" [attr.aria-label]="'Hide password'" [attr.aria-pressed]="hide">
                <mat-icon>{{ hide ? 'visibility_off' : 'visibility' }}</mat-icon>
              </button>
            </mat-form-field>
          </div>
          <div class="check-me">
            <mat-checkbox class="float-start" checked color="primary">Remember me?</mat-checkbox>
            <a class="f-w-400 f-14 text-primary-500 m-b-0" href="javascript:">Forgot Password </a>
          </div>
          <div class="grid">
            <button mat-flat-button color="primary" class="b-rad-20 m-t-15">Login</button>
          </div>
          <div class="check-me m-t-20">
            <h6 class="f-w-400 text-muted m-b-0">Don't have an Account?</h6>
            <a [routerLink]="['/auth/register']" class="text-primary-500">Create Account</a>
          </div>
        </app-card>
      </div>
    </div>
  </div>
</div>
