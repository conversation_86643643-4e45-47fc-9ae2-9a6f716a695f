/**======================================================================
=========================================================================
Template Name: Able Pro - Bootstrap Admin Template
Author: Phoenixcoded
Support: https://phoenixcoded.authordesk.app
File: style.css
=========================================================================
=================================================================================== */
body {
  font-feature-settings: "salt";
}

h1,
h2 {
  font-weight: 700;
}

/* $btn-border-radius: 12px;
$btn-border-radius-sm: 8px;
$btn-border-radius-lg: 14px; */
:root {
  --bs-body-bg: #f8f9fa;
  --bs-body-bg-rgb: 248, 249, 250;
  --pc-heading-color: #1d2630;
  --pc-active-background: #f3f5f7;
  --pc-sidebar-background: transparent;
  --pc-sidebar-color: #5b6b79;
  --pc-sidebar-color-rgb: 91, 107, 121;
  --pc-sidebar-active-color: #0d6efd;
  --pc-sidebar-shadow: none;
  --pc-sidebar-caption-color: #3e4853;
  --pc-sidebar-border: 1px dashed #bec8d0;
  --pc-sidebar-user-background: #f3f5f7;
  --pc-header-background: rgba(var(--bs-body-bg-rgb), 0.7);
  --pc-header-color: #5b6b79;
  --pc-header-shadow: none;
  --pc-card-box-shadow: none;
  --pc-header-submenu-background: #ffffff;
  --pc-header-submenu-color: #5b6b79;
}

[data-pc-theme_contrast=true] {
  --bs-body-bg: #ffffff;
  --pc-sidebar-background: transparent;
  --pc-sidebar-active-color: #0d6efd;
  --pc-sidebar-shadow: 1px 0 3px 0px #dbe0e5;
  --pc-sidebar-border: none;
  --pc-card-box-shadow: 0px 8px 24px rgba(27, 46, 94, 0.08);
}

[data-pc-preset=preset-1] {
  --pc-sidebar-active-color: #4680ff;
  --bs-blue: #4680ff;
  --bs-primary: #4680ff;
  --bs-primary-rgb: 70, 128, 255;
  --bs-primary-light: #edf2ff;
  --bs-link-color: #4680ff;
  --bs-link-color-rgb: 70, 128, 255;
  --bs-link-hover-color: #3866cc;
  --bs-link-hover-color-rgb: to-rgb(shift-color($pc-primary, $link-shade-percentage));
  --dt-row-selected: 70, 128, 255;
}
[data-pc-preset=preset-1] .bg-light-primary {
  background: #edf2ff;
  color: #4680ff;
}
[data-pc-preset=preset-1] .link-primary {
  color: #4680ff !important;
}
[data-pc-preset=preset-1] .link-primary:hover, [data-pc-preset=preset-1] .link-primary:focus {
  color: #3866cc !important;
}
[data-pc-preset=preset-1] .btn-primary {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #4680ff;
  --bs-btn-border-color: #4680ff;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #3c6dd9;
  --bs-btn-hover-border-color: #3866cc;
  --bs-btn-focus-shadow-rgb: 98, 147, 255;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #3866cc;
  --bs-btn-active-border-color: #3560bf;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #4680ff;
  --bs-btn-disabled-border-color: #4680ff;
}
[data-pc-preset=preset-1] .btn-link {
  --bs-btn-color: #4680ff;
  --bs-btn-hover-color: #3866cc;
  --bs-btn-active-color: #3866cc;
}
[data-pc-preset=preset-1] .text-bg-primary {
  color: #ffffff !important;
  background-color: RGBA(70, 128, 255, var(--bs-bg-opacity, 1)) !important;
}
[data-pc-preset=preset-1] .accordion {
  --bs-accordion-btn-focus-border-color: #4680ff;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.2rem rgba(70, 128, 255, 0.25);
  --bs-accordion-active-color: #4680ff;
  --bs-accordion-active-bg: #edf2ff;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%234680ff'%3e%3cpath fill-rule=' evenodd' d=' M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-1] .alert-primary {
  --bs-alert-color: #2a4d99;
  --bs-alert-bg: #dae6ff;
  --bs-alert-border-color: #c8d9ff;
  --bs-alert-link-color: #223e7a;
}
[data-pc-preset=preset-1] .list-group {
  --bs-list-group-active-bg: #4680ff;
  --bs-list-group-active-border-color: #4680ff;
}
[data-pc-preset=preset-1] .list-group-item-primary {
  color: #2a4d99;
  background-color: #dae6ff;
}
[data-pc-preset=preset-1] .nav {
  --bs-nav-link-hover-color: #3866cc;
}
[data-pc-preset=preset-1] .nav-pills {
  --bs-nav-pills-link-active-bg: #4680ff;
}
[data-pc-preset=preset-1] .pagination {
  --bs-pagination-hover-color: #3866cc;
  --bs-pagination-focus-color: #3866cc;
  --bs-pagination-focus-box-shadow: 0 0 0 0.2rem rgba(70, 128, 255, 0.25);
  --bs-pagination-active-bg: #4680ff;
  --bs-pagination-active-border-color: #4680ff;
}
[data-pc-preset=preset-1] .progress {
  --bs-progress-bar-bg: #4680ff;
}
[data-pc-preset=preset-1] .form-check .form-check-input.input-primary:checked {
  border-color: #4680ff;
  background-color: #4680ff;
}
[data-pc-preset=preset-1] .form-check .form-check-input.input-light-primary:checked {
  border-color: #edf2ff;
  background-color: #edf2ff;
}
[data-pc-preset=preset-1] .form-check .form-check-input.input-light-primary:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%234680ff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-1] .form-check .form-check-input.input-light-primary:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%234680ff'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-1] .form-check .form-check-input.input-primary:focus[type=checkbox], [data-pc-preset=preset-1] .form-check .form-check-input.input-primary:focus[type=radio], [data-pc-preset=preset-1] .form-check .form-check-input.input-light-primary:focus[type=checkbox], [data-pc-preset=preset-1] .form-check .form-check-input.input-light-primary:focus[type=radio] {
  box-shadow: 0 0 0 0.2rem rgba(70, 128, 255, 0.25);
  border-color: #4680ff;
}
[data-pc-preset=preset-1] .form-check.form-switch .form-check-input.input-light-primary:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%234680ff'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-1] .btn-light-primary {
  background: #edf2ff;
  color: #4680ff;
  border-color: #edf2ff;
}
[data-pc-preset=preset-1] .btn-light-primary .material-icons-two-tone {
  background-color: #4680ff;
}
[data-pc-preset=preset-1] .btn-light-primary:hover {
  background: #4680ff;
  color: #fff;
  border-color: #4680ff;
}
[data-pc-preset=preset-1] .btn-light-primary.focus, [data-pc-preset=preset-1] .btn-light-primary:focus {
  background: #4680ff;
  color: #fff;
  border-color: #4680ff;
}
[data-pc-preset=preset-1] .btn-light-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-1] .btn-light-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-1] .btn-light-primary.dropdown-toggle {
  background: #4680ff;
  color: #fff;
  border-color: #4680ff;
}
[data-pc-preset=preset-1] .btn-check:active + .btn-light-primary,
[data-pc-preset=preset-1] .btn-check:checked + .btn-light-primary {
  background: #4680ff;
  color: #fff;
  border-color: #4680ff;
}
[data-pc-preset=preset-1] .btn-link-primary {
  background: transparent;
  color: #4680ff;
  border-color: transparent;
}
[data-pc-preset=preset-1] .btn-link-primary .material-icons-two-tone {
  background-color: #4680ff;
}
[data-pc-preset=preset-1] .btn-link-primary:hover {
  background: #edf2ff;
  color: #4680ff;
  border-color: #edf2ff;
}
[data-pc-preset=preset-1] .btn-link-primary.focus, [data-pc-preset=preset-1] .btn-link-primary:focus {
  background: #edf2ff;
  color: #4680ff;
  border-color: #edf2ff;
}
[data-pc-preset=preset-1] .btn-link-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-1] .btn-link-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-1] .btn-link-primary.dropdown-toggle {
  background: #edf2ff;
  color: #4680ff;
  border-color: #edf2ff;
}
[data-pc-preset=preset-1] .btn-check:active + .btn-link-primary,
[data-pc-preset=preset-1] .btn-check:checked + .btn-link-primary {
  background: #edf2ff;
  color: #4680ff;
  border-color: #edf2ff;
}

[data-pc-preset=preset-2] {
  --pc-sidebar-active-color: #6610f2;
  --bs-blue: #6610f2;
  --bs-primary: #6610f2;
  --bs-primary-rgb: 102, 16, 242;
  --bs-primary-light: #f0e7fe;
  --bs-link-color: #6610f2;
  --bs-link-color-rgb: 102, 16, 242;
  --bs-link-hover-color: #520dc2;
  --bs-link-hover-color-rgb: to-rgb(shift-color($pc-primary, $link-shade-percentage));
  --dt-row-selected: 102, 16, 242;
}
[data-pc-preset=preset-2] .bg-light-primary {
  background: #f0e7fe;
  color: #6610f2;
}
[data-pc-preset=preset-2] .link-primary {
  color: #6610f2 !important;
}
[data-pc-preset=preset-2] .link-primary:hover, [data-pc-preset=preset-2] .link-primary:focus {
  color: #520dc2 !important;
}
[data-pc-preset=preset-2] .btn-primary {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #6610f2;
  --bs-btn-border-color: #6610f2;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #570ece;
  --bs-btn-hover-border-color: #520dc2;
  --bs-btn-focus-shadow-rgb: 125, 52, 244;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #520dc2;
  --bs-btn-active-border-color: #4d0cb6;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #6610f2;
  --bs-btn-disabled-border-color: #6610f2;
}
[data-pc-preset=preset-2] .btn-link {
  --bs-btn-color: #6610f2;
  --bs-btn-hover-color: #520dc2;
  --bs-btn-active-color: #520dc2;
}
[data-pc-preset=preset-2] .text-bg-primary {
  color: #ffffff !important;
  background-color: RGBA(102, 16, 242, var(--bs-bg-opacity, 1)) !important;
}
[data-pc-preset=preset-2] .accordion {
  --bs-accordion-btn-focus-border-color: #6610f2;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.2rem rgba(102, 16, 242, 0.25);
  --bs-accordion-active-color: #6610f2;
  --bs-accordion-active-bg: #f0e7fe;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%236610f2'%3e%3cpath fill-rule=' evenodd' d=' M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-2] .alert-primary {
  --bs-alert-color: #3d0a91;
  --bs-alert-bg: #e0cffc;
  --bs-alert-border-color: #d1b7fb;
  --bs-alert-link-color: #310874;
}
[data-pc-preset=preset-2] .list-group {
  --bs-list-group-active-bg: #6610f2;
  --bs-list-group-active-border-color: #6610f2;
}
[data-pc-preset=preset-2] .list-group-item-primary {
  color: #3d0a91;
  background-color: #e0cffc;
}
[data-pc-preset=preset-2] .nav {
  --bs-nav-link-hover-color: #520dc2;
}
[data-pc-preset=preset-2] .nav-pills {
  --bs-nav-pills-link-active-bg: #6610f2;
}
[data-pc-preset=preset-2] .pagination {
  --bs-pagination-hover-color: #520dc2;
  --bs-pagination-focus-color: #520dc2;
  --bs-pagination-focus-box-shadow: 0 0 0 0.2rem rgba(102, 16, 242, 0.25);
  --bs-pagination-active-bg: #6610f2;
  --bs-pagination-active-border-color: #6610f2;
}
[data-pc-preset=preset-2] .progress {
  --bs-progress-bar-bg: #6610f2;
}
[data-pc-preset=preset-2] .form-check .form-check-input.input-primary:checked {
  border-color: #6610f2;
  background-color: #6610f2;
}
[data-pc-preset=preset-2] .form-check .form-check-input.input-light-primary:checked {
  border-color: #f0e7fe;
  background-color: #f0e7fe;
}
[data-pc-preset=preset-2] .form-check .form-check-input.input-light-primary:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%236610f2' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-2] .form-check .form-check-input.input-light-primary:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%236610f2'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-2] .form-check .form-check-input.input-primary:focus[type=checkbox], [data-pc-preset=preset-2] .form-check .form-check-input.input-primary:focus[type=radio], [data-pc-preset=preset-2] .form-check .form-check-input.input-light-primary:focus[type=checkbox], [data-pc-preset=preset-2] .form-check .form-check-input.input-light-primary:focus[type=radio] {
  box-shadow: 0 0 0 0.2rem rgba(102, 16, 242, 0.25);
  border-color: #6610f2;
}
[data-pc-preset=preset-2] .form-check.form-switch .form-check-input.input-light-primary:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%236610f2'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-2] .btn-light-primary {
  background: #f0e7fe;
  color: #6610f2;
  border-color: #f0e7fe;
}
[data-pc-preset=preset-2] .btn-light-primary .material-icons-two-tone {
  background-color: #6610f2;
}
[data-pc-preset=preset-2] .btn-light-primary:hover {
  background: #6610f2;
  color: #fff;
  border-color: #6610f2;
}
[data-pc-preset=preset-2] .btn-light-primary.focus, [data-pc-preset=preset-2] .btn-light-primary:focus {
  background: #6610f2;
  color: #fff;
  border-color: #6610f2;
}
[data-pc-preset=preset-2] .btn-light-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-2] .btn-light-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-2] .btn-light-primary.dropdown-toggle {
  background: #6610f2;
  color: #fff;
  border-color: #6610f2;
}
[data-pc-preset=preset-2] .btn-check:active + .btn-light-primary,
[data-pc-preset=preset-2] .btn-check:checked + .btn-light-primary {
  background: #6610f2;
  color: #fff;
  border-color: #6610f2;
}
[data-pc-preset=preset-2] .btn-link-primary {
  background: transparent;
  color: #6610f2;
  border-color: transparent;
}
[data-pc-preset=preset-2] .btn-link-primary .material-icons-two-tone {
  background-color: #6610f2;
}
[data-pc-preset=preset-2] .btn-link-primary:hover {
  background: #f0e7fe;
  color: #6610f2;
  border-color: #f0e7fe;
}
[data-pc-preset=preset-2] .btn-link-primary.focus, [data-pc-preset=preset-2] .btn-link-primary:focus {
  background: #f0e7fe;
  color: #6610f2;
  border-color: #f0e7fe;
}
[data-pc-preset=preset-2] .btn-link-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-2] .btn-link-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-2] .btn-link-primary.dropdown-toggle {
  background: #f0e7fe;
  color: #6610f2;
  border-color: #f0e7fe;
}
[data-pc-preset=preset-2] .btn-check:active + .btn-link-primary,
[data-pc-preset=preset-2] .btn-check:checked + .btn-link-primary {
  background: #f0e7fe;
  color: #6610f2;
  border-color: #f0e7fe;
}

[data-pc-preset=preset-3] {
  --pc-sidebar-active-color: #673ab7;
  --bs-blue: #673ab7;
  --bs-primary: #673ab7;
  --bs-primary-rgb: 103, 58, 183;
  --bs-primary-light: #f0ebf8;
  --bs-link-color: #673ab7;
  --bs-link-color-rgb: 103, 58, 183;
  --bs-link-hover-color: #522e92;
  --bs-link-hover-color-rgb: to-rgb(shift-color($pc-primary, $link-shade-percentage));
  --dt-row-selected: 103, 58, 183;
}
[data-pc-preset=preset-3] .bg-light-primary {
  background: #f0ebf8;
  color: #673ab7;
}
[data-pc-preset=preset-3] .link-primary {
  color: #673ab7 !important;
}
[data-pc-preset=preset-3] .link-primary:hover, [data-pc-preset=preset-3] .link-primary:focus {
  color: #522e92 !important;
}
[data-pc-preset=preset-3] .btn-primary {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #673ab7;
  --bs-btn-border-color: #673ab7;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #58319c;
  --bs-btn-hover-border-color: #522e92;
  --bs-btn-focus-shadow-rgb: 126, 88, 194;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #522e92;
  --bs-btn-active-border-color: #4d2c89;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #673ab7;
  --bs-btn-disabled-border-color: #673ab7;
}
[data-pc-preset=preset-3] .btn-link {
  --bs-btn-color: #673ab7;
  --bs-btn-hover-color: #522e92;
  --bs-btn-active-color: #522e92;
}
[data-pc-preset=preset-3] .text-bg-primary {
  color: #ffffff !important;
  background-color: RGBA(103, 58, 183, var(--bs-bg-opacity, 1)) !important;
}
[data-pc-preset=preset-3] .accordion {
  --bs-accordion-btn-focus-border-color: #673ab7;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.2rem rgba(103, 58, 183, 0.25);
  --bs-accordion-active-color: #673ab7;
  --bs-accordion-active-bg: #f0ebf8;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23673ab7'%3e%3cpath fill-rule=' evenodd' d=' M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-3] .alert-primary {
  --bs-alert-color: #3e236e;
  --bs-alert-bg: #e1d8f1;
  --bs-alert-border-color: #d1c4e9;
  --bs-alert-link-color: #321c58;
}
[data-pc-preset=preset-3] .list-group {
  --bs-list-group-active-bg: #673ab7;
  --bs-list-group-active-border-color: #673ab7;
}
[data-pc-preset=preset-3] .list-group-item-primary {
  color: #3e236e;
  background-color: #e1d8f1;
}
[data-pc-preset=preset-3] .nav {
  --bs-nav-link-hover-color: #522e92;
}
[data-pc-preset=preset-3] .nav-pills {
  --bs-nav-pills-link-active-bg: #673ab7;
}
[data-pc-preset=preset-3] .pagination {
  --bs-pagination-hover-color: #522e92;
  --bs-pagination-focus-color: #522e92;
  --bs-pagination-focus-box-shadow: 0 0 0 0.2rem rgba(103, 58, 183, 0.25);
  --bs-pagination-active-bg: #673ab7;
  --bs-pagination-active-border-color: #673ab7;
}
[data-pc-preset=preset-3] .progress {
  --bs-progress-bar-bg: #673ab7;
}
[data-pc-preset=preset-3] .form-check .form-check-input.input-primary:checked {
  border-color: #673ab7;
  background-color: #673ab7;
}
[data-pc-preset=preset-3] .form-check .form-check-input.input-light-primary:checked {
  border-color: #f0ebf8;
  background-color: #f0ebf8;
}
[data-pc-preset=preset-3] .form-check .form-check-input.input-light-primary:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23673ab7' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-3] .form-check .form-check-input.input-light-primary:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23673ab7'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-3] .form-check .form-check-input.input-primary:focus[type=checkbox], [data-pc-preset=preset-3] .form-check .form-check-input.input-primary:focus[type=radio], [data-pc-preset=preset-3] .form-check .form-check-input.input-light-primary:focus[type=checkbox], [data-pc-preset=preset-3] .form-check .form-check-input.input-light-primary:focus[type=radio] {
  box-shadow: 0 0 0 0.2rem rgba(103, 58, 183, 0.25);
  border-color: #673ab7;
}
[data-pc-preset=preset-3] .form-check.form-switch .form-check-input.input-light-primary:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23673ab7'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-3] .btn-light-primary {
  background: #f0ebf8;
  color: #673ab7;
  border-color: #f0ebf8;
}
[data-pc-preset=preset-3] .btn-light-primary .material-icons-two-tone {
  background-color: #673ab7;
}
[data-pc-preset=preset-3] .btn-light-primary:hover {
  background: #673ab7;
  color: #fff;
  border-color: #673ab7;
}
[data-pc-preset=preset-3] .btn-light-primary.focus, [data-pc-preset=preset-3] .btn-light-primary:focus {
  background: #673ab7;
  color: #fff;
  border-color: #673ab7;
}
[data-pc-preset=preset-3] .btn-light-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-3] .btn-light-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-3] .btn-light-primary.dropdown-toggle {
  background: #673ab7;
  color: #fff;
  border-color: #673ab7;
}
[data-pc-preset=preset-3] .btn-check:active + .btn-light-primary,
[data-pc-preset=preset-3] .btn-check:checked + .btn-light-primary {
  background: #673ab7;
  color: #fff;
  border-color: #673ab7;
}
[data-pc-preset=preset-3] .btn-link-primary {
  background: transparent;
  color: #673ab7;
  border-color: transparent;
}
[data-pc-preset=preset-3] .btn-link-primary .material-icons-two-tone {
  background-color: #673ab7;
}
[data-pc-preset=preset-3] .btn-link-primary:hover {
  background: #f0ebf8;
  color: #673ab7;
  border-color: #f0ebf8;
}
[data-pc-preset=preset-3] .btn-link-primary.focus, [data-pc-preset=preset-3] .btn-link-primary:focus {
  background: #f0ebf8;
  color: #673ab7;
  border-color: #f0ebf8;
}
[data-pc-preset=preset-3] .btn-link-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-3] .btn-link-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-3] .btn-link-primary.dropdown-toggle {
  background: #f0ebf8;
  color: #673ab7;
  border-color: #f0ebf8;
}
[data-pc-preset=preset-3] .btn-check:active + .btn-link-primary,
[data-pc-preset=preset-3] .btn-check:checked + .btn-link-primary {
  background: #f0ebf8;
  color: #673ab7;
  border-color: #f0ebf8;
}

[data-pc-preset=preset-4] {
  --pc-sidebar-active-color: #e83e8c;
  --bs-blue: #e83e8c;
  --bs-primary: #e83e8c;
  --bs-primary-rgb: 232, 62, 140;
  --bs-primary-light: #fdecf4;
  --bs-link-color: #e83e8c;
  --bs-link-color-rgb: 232, 62, 140;
  --bs-link-hover-color: #ba3270;
  --bs-link-hover-color-rgb: to-rgb(shift-color($pc-primary, $link-shade-percentage));
  --dt-row-selected: 232, 62, 140;
}
[data-pc-preset=preset-4] .bg-light-primary {
  background: #fdecf4;
  color: #e83e8c;
}
[data-pc-preset=preset-4] .link-primary {
  color: #e83e8c !important;
}
[data-pc-preset=preset-4] .link-primary:hover, [data-pc-preset=preset-4] .link-primary:focus {
  color: #ba3270 !important;
}
[data-pc-preset=preset-4] .btn-primary {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #e83e8c;
  --bs-btn-border-color: #e83e8c;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #c53577;
  --bs-btn-hover-border-color: #ba3270;
  --bs-btn-focus-shadow-rgb: 235, 91, 157;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #ba3270;
  --bs-btn-active-border-color: #ae2f69;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #e83e8c;
  --bs-btn-disabled-border-color: #e83e8c;
}
[data-pc-preset=preset-4] .btn-link {
  --bs-btn-color: #e83e8c;
  --bs-btn-hover-color: #ba3270;
  --bs-btn-active-color: #ba3270;
}
[data-pc-preset=preset-4] .text-bg-primary {
  color: #ffffff !important;
  background-color: RGBA(232, 62, 140, var(--bs-bg-opacity, 1)) !important;
}
[data-pc-preset=preset-4] .accordion {
  --bs-accordion-btn-focus-border-color: #e83e8c;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.2rem rgba(232, 62, 140, 0.25);
  --bs-accordion-active-color: #e83e8c;
  --bs-accordion-active-bg: #fdecf4;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23e83e8c'%3e%3cpath fill-rule=' evenodd' d=' M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-4] .alert-primary {
  --bs-alert-color: #8b2554;
  --bs-alert-bg: #fad8e8;
  --bs-alert-border-color: #f8c5dd;
  --bs-alert-link-color: #6f1e43;
}
[data-pc-preset=preset-4] .list-group {
  --bs-list-group-active-bg: #e83e8c;
  --bs-list-group-active-border-color: #e83e8c;
}
[data-pc-preset=preset-4] .list-group-item-primary {
  color: #8b2554;
  background-color: #fad8e8;
}
[data-pc-preset=preset-4] .nav {
  --bs-nav-link-hover-color: #ba3270;
}
[data-pc-preset=preset-4] .nav-pills {
  --bs-nav-pills-link-active-bg: #e83e8c;
}
[data-pc-preset=preset-4] .pagination {
  --bs-pagination-hover-color: #ba3270;
  --bs-pagination-focus-color: #ba3270;
  --bs-pagination-focus-box-shadow: 0 0 0 0.2rem rgba(232, 62, 140, 0.25);
  --bs-pagination-active-bg: #e83e8c;
  --bs-pagination-active-border-color: #e83e8c;
}
[data-pc-preset=preset-4] .progress {
  --bs-progress-bar-bg: #e83e8c;
}
[data-pc-preset=preset-4] .form-check .form-check-input.input-primary:checked {
  border-color: #e83e8c;
  background-color: #e83e8c;
}
[data-pc-preset=preset-4] .form-check .form-check-input.input-light-primary:checked {
  border-color: #fdecf4;
  background-color: #fdecf4;
}
[data-pc-preset=preset-4] .form-check .form-check-input.input-light-primary:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23e83e8c' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-4] .form-check .form-check-input.input-light-primary:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23e83e8c'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-4] .form-check .form-check-input.input-primary:focus[type=checkbox], [data-pc-preset=preset-4] .form-check .form-check-input.input-primary:focus[type=radio], [data-pc-preset=preset-4] .form-check .form-check-input.input-light-primary:focus[type=checkbox], [data-pc-preset=preset-4] .form-check .form-check-input.input-light-primary:focus[type=radio] {
  box-shadow: 0 0 0 0.2rem rgba(232, 62, 140, 0.25);
  border-color: #e83e8c;
}
[data-pc-preset=preset-4] .form-check.form-switch .form-check-input.input-light-primary:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23e83e8c'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-4] .btn-light-primary {
  background: #fdecf4;
  color: #e83e8c;
  border-color: #fdecf4;
}
[data-pc-preset=preset-4] .btn-light-primary .material-icons-two-tone {
  background-color: #e83e8c;
}
[data-pc-preset=preset-4] .btn-light-primary:hover {
  background: #e83e8c;
  color: #fff;
  border-color: #e83e8c;
}
[data-pc-preset=preset-4] .btn-light-primary.focus, [data-pc-preset=preset-4] .btn-light-primary:focus {
  background: #e83e8c;
  color: #fff;
  border-color: #e83e8c;
}
[data-pc-preset=preset-4] .btn-light-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-4] .btn-light-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-4] .btn-light-primary.dropdown-toggle {
  background: #e83e8c;
  color: #fff;
  border-color: #e83e8c;
}
[data-pc-preset=preset-4] .btn-check:active + .btn-light-primary,
[data-pc-preset=preset-4] .btn-check:checked + .btn-light-primary {
  background: #e83e8c;
  color: #fff;
  border-color: #e83e8c;
}
[data-pc-preset=preset-4] .btn-link-primary {
  background: transparent;
  color: #e83e8c;
  border-color: transparent;
}
[data-pc-preset=preset-4] .btn-link-primary .material-icons-two-tone {
  background-color: #e83e8c;
}
[data-pc-preset=preset-4] .btn-link-primary:hover {
  background: #fdecf4;
  color: #e83e8c;
  border-color: #fdecf4;
}
[data-pc-preset=preset-4] .btn-link-primary.focus, [data-pc-preset=preset-4] .btn-link-primary:focus {
  background: #fdecf4;
  color: #e83e8c;
  border-color: #fdecf4;
}
[data-pc-preset=preset-4] .btn-link-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-4] .btn-link-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-4] .btn-link-primary.dropdown-toggle {
  background: #fdecf4;
  color: #e83e8c;
  border-color: #fdecf4;
}
[data-pc-preset=preset-4] .btn-check:active + .btn-link-primary,
[data-pc-preset=preset-4] .btn-check:checked + .btn-link-primary {
  background: #fdecf4;
  color: #e83e8c;
  border-color: #fdecf4;
}

[data-pc-preset=preset-5] {
  --pc-sidebar-active-color: #dc2626;
  --bs-blue: #dc2626;
  --bs-primary: #dc2626;
  --bs-primary-rgb: 220, 38, 38;
  --bs-primary-light: #fce9e9;
  --bs-link-color: #dc2626;
  --bs-link-color-rgb: 220, 38, 38;
  --bs-link-hover-color: #b01e1e;
  --bs-link-hover-color-rgb: to-rgb(shift-color($pc-primary, $link-shade-percentage));
  --dt-row-selected: 220, 38, 38;
}
[data-pc-preset=preset-5] .bg-light-primary {
  background: #fce9e9;
  color: #dc2626;
}
[data-pc-preset=preset-5] .link-primary {
  color: #dc2626 !important;
}
[data-pc-preset=preset-5] .link-primary:hover, [data-pc-preset=preset-5] .link-primary:focus {
  color: #b01e1e !important;
}
[data-pc-preset=preset-5] .btn-primary {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #dc2626;
  --bs-btn-border-color: #dc2626;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #bb2020;
  --bs-btn-hover-border-color: #b01e1e;
  --bs-btn-focus-shadow-rgb: 225, 71, 71;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #b01e1e;
  --bs-btn-active-border-color: #a51d1d;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #dc2626;
  --bs-btn-disabled-border-color: #dc2626;
}
[data-pc-preset=preset-5] .btn-link {
  --bs-btn-color: #dc2626;
  --bs-btn-hover-color: #b01e1e;
  --bs-btn-active-color: #b01e1e;
}
[data-pc-preset=preset-5] .text-bg-primary {
  color: #ffffff !important;
  background-color: RGBA(220, 38, 38, var(--bs-bg-opacity, 1)) !important;
}
[data-pc-preset=preset-5] .accordion {
  --bs-accordion-btn-focus-border-color: #dc2626;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
  --bs-accordion-active-color: #dc2626;
  --bs-accordion-active-bg: #fce9e9;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23dc2626'%3e%3cpath fill-rule=' evenodd' d=' M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-5] .alert-primary {
  --bs-alert-color: #841717;
  --bs-alert-bg: #f8d4d4;
  --bs-alert-border-color: #f5bebe;
  --bs-alert-link-color: #6a1212;
}
[data-pc-preset=preset-5] .list-group {
  --bs-list-group-active-bg: #dc2626;
  --bs-list-group-active-border-color: #dc2626;
}
[data-pc-preset=preset-5] .list-group-item-primary {
  color: #841717;
  background-color: #f8d4d4;
}
[data-pc-preset=preset-5] .nav {
  --bs-nav-link-hover-color: #b01e1e;
}
[data-pc-preset=preset-5] .nav-pills {
  --bs-nav-pills-link-active-bg: #dc2626;
}
[data-pc-preset=preset-5] .pagination {
  --bs-pagination-hover-color: #b01e1e;
  --bs-pagination-focus-color: #b01e1e;
  --bs-pagination-focus-box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
  --bs-pagination-active-bg: #dc2626;
  --bs-pagination-active-border-color: #dc2626;
}
[data-pc-preset=preset-5] .progress {
  --bs-progress-bar-bg: #dc2626;
}
[data-pc-preset=preset-5] .form-check .form-check-input.input-primary:checked {
  border-color: #dc2626;
  background-color: #dc2626;
}
[data-pc-preset=preset-5] .form-check .form-check-input.input-light-primary:checked {
  border-color: #fce9e9;
  background-color: #fce9e9;
}
[data-pc-preset=preset-5] .form-check .form-check-input.input-light-primary:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23dc2626' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-5] .form-check .form-check-input.input-light-primary:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23dc2626'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-5] .form-check .form-check-input.input-primary:focus[type=checkbox], [data-pc-preset=preset-5] .form-check .form-check-input.input-primary:focus[type=radio], [data-pc-preset=preset-5] .form-check .form-check-input.input-light-primary:focus[type=checkbox], [data-pc-preset=preset-5] .form-check .form-check-input.input-light-primary:focus[type=radio] {
  box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
  border-color: #dc2626;
}
[data-pc-preset=preset-5] .form-check.form-switch .form-check-input.input-light-primary:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23dc2626'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-5] .btn-light-primary {
  background: #fce9e9;
  color: #dc2626;
  border-color: #fce9e9;
}
[data-pc-preset=preset-5] .btn-light-primary .material-icons-two-tone {
  background-color: #dc2626;
}
[data-pc-preset=preset-5] .btn-light-primary:hover {
  background: #dc2626;
  color: #fff;
  border-color: #dc2626;
}
[data-pc-preset=preset-5] .btn-light-primary.focus, [data-pc-preset=preset-5] .btn-light-primary:focus {
  background: #dc2626;
  color: #fff;
  border-color: #dc2626;
}
[data-pc-preset=preset-5] .btn-light-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-5] .btn-light-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-5] .btn-light-primary.dropdown-toggle {
  background: #dc2626;
  color: #fff;
  border-color: #dc2626;
}
[data-pc-preset=preset-5] .btn-check:active + .btn-light-primary,
[data-pc-preset=preset-5] .btn-check:checked + .btn-light-primary {
  background: #dc2626;
  color: #fff;
  border-color: #dc2626;
}
[data-pc-preset=preset-5] .btn-link-primary {
  background: transparent;
  color: #dc2626;
  border-color: transparent;
}
[data-pc-preset=preset-5] .btn-link-primary .material-icons-two-tone {
  background-color: #dc2626;
}
[data-pc-preset=preset-5] .btn-link-primary:hover {
  background: #fce9e9;
  color: #dc2626;
  border-color: #fce9e9;
}
[data-pc-preset=preset-5] .btn-link-primary.focus, [data-pc-preset=preset-5] .btn-link-primary:focus {
  background: #fce9e9;
  color: #dc2626;
  border-color: #fce9e9;
}
[data-pc-preset=preset-5] .btn-link-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-5] .btn-link-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-5] .btn-link-primary.dropdown-toggle {
  background: #fce9e9;
  color: #dc2626;
  border-color: #fce9e9;
}
[data-pc-preset=preset-5] .btn-check:active + .btn-link-primary,
[data-pc-preset=preset-5] .btn-check:checked + .btn-link-primary {
  background: #fce9e9;
  color: #dc2626;
  border-color: #fce9e9;
}

[data-pc-preset=preset-6] {
  --pc-sidebar-active-color: #fd7e14;
  --bs-blue: #fd7e14;
  --bs-primary: #fd7e14;
  --bs-primary-rgb: 253, 126, 20;
  --bs-primary-light: #fff2e8;
  --bs-link-color: #fd7e14;
  --bs-link-color-rgb: 253, 126, 20;
  --bs-link-hover-color: #ca6510;
  --bs-link-hover-color-rgb: to-rgb(shift-color($pc-primary, $link-shade-percentage));
  --dt-row-selected: 253, 126, 20;
}
[data-pc-preset=preset-6] .bg-light-primary {
  background: #fff2e8;
  color: #fd7e14;
}
[data-pc-preset=preset-6] .link-primary {
  color: #fd7e14 !important;
}
[data-pc-preset=preset-6] .link-primary:hover, [data-pc-preset=preset-6] .link-primary:focus {
  color: #ca6510 !important;
}
[data-pc-preset=preset-6] .btn-primary {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #fd7e14;
  --bs-btn-border-color: #fd7e14;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #d76b11;
  --bs-btn-hover-border-color: #ca6510;
  --bs-btn-focus-shadow-rgb: 253, 145, 55;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #ca6510;
  --bs-btn-active-border-color: #be5f0f;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #fd7e14;
  --bs-btn-disabled-border-color: #fd7e14;
}
[data-pc-preset=preset-6] .btn-link {
  --bs-btn-color: #fd7e14;
  --bs-btn-hover-color: #ca6510;
  --bs-btn-active-color: #ca6510;
}
[data-pc-preset=preset-6] .text-bg-primary {
  color: #ffffff !important;
  background-color: RGBA(253, 126, 20, var(--bs-bg-opacity, 1)) !important;
}
[data-pc-preset=preset-6] .accordion {
  --bs-accordion-btn-focus-border-color: #fd7e14;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.2rem rgba(253, 126, 20, 0.25);
  --bs-accordion-active-color: #fd7e14;
  --bs-accordion-active-bg: #fff2e8;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fd7e14'%3e%3cpath fill-rule=' evenodd' d=' M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-6] .alert-primary {
  --bs-alert-color: #984c0c;
  --bs-alert-bg: #ffe5d0;
  --bs-alert-border-color: #fed8b9;
  --bs-alert-link-color: #7a3d0a;
}
[data-pc-preset=preset-6] .list-group {
  --bs-list-group-active-bg: #fd7e14;
  --bs-list-group-active-border-color: #fd7e14;
}
[data-pc-preset=preset-6] .list-group-item-primary {
  color: #984c0c;
  background-color: #ffe5d0;
}
[data-pc-preset=preset-6] .nav {
  --bs-nav-link-hover-color: #ca6510;
}
[data-pc-preset=preset-6] .nav-pills {
  --bs-nav-pills-link-active-bg: #fd7e14;
}
[data-pc-preset=preset-6] .pagination {
  --bs-pagination-hover-color: #ca6510;
  --bs-pagination-focus-color: #ca6510;
  --bs-pagination-focus-box-shadow: 0 0 0 0.2rem rgba(253, 126, 20, 0.25);
  --bs-pagination-active-bg: #fd7e14;
  --bs-pagination-active-border-color: #fd7e14;
}
[data-pc-preset=preset-6] .progress {
  --bs-progress-bar-bg: #fd7e14;
}
[data-pc-preset=preset-6] .form-check .form-check-input.input-primary:checked {
  border-color: #fd7e14;
  background-color: #fd7e14;
}
[data-pc-preset=preset-6] .form-check .form-check-input.input-light-primary:checked {
  border-color: #fff2e8;
  background-color: #fff2e8;
}
[data-pc-preset=preset-6] .form-check .form-check-input.input-light-primary:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fd7e14' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-6] .form-check .form-check-input.input-light-primary:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fd7e14'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-6] .form-check .form-check-input.input-primary:focus[type=checkbox], [data-pc-preset=preset-6] .form-check .form-check-input.input-primary:focus[type=radio], [data-pc-preset=preset-6] .form-check .form-check-input.input-light-primary:focus[type=checkbox], [data-pc-preset=preset-6] .form-check .form-check-input.input-light-primary:focus[type=radio] {
  box-shadow: 0 0 0 0.2rem rgba(253, 126, 20, 0.25);
  border-color: #fd7e14;
}
[data-pc-preset=preset-6] .form-check.form-switch .form-check-input.input-light-primary:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fd7e14'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-6] .btn-light-primary {
  background: #fff2e8;
  color: #fd7e14;
  border-color: #fff2e8;
}
[data-pc-preset=preset-6] .btn-light-primary .material-icons-two-tone {
  background-color: #fd7e14;
}
[data-pc-preset=preset-6] .btn-light-primary:hover {
  background: #fd7e14;
  color: #fff;
  border-color: #fd7e14;
}
[data-pc-preset=preset-6] .btn-light-primary.focus, [data-pc-preset=preset-6] .btn-light-primary:focus {
  background: #fd7e14;
  color: #fff;
  border-color: #fd7e14;
}
[data-pc-preset=preset-6] .btn-light-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-6] .btn-light-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-6] .btn-light-primary.dropdown-toggle {
  background: #fd7e14;
  color: #fff;
  border-color: #fd7e14;
}
[data-pc-preset=preset-6] .btn-check:active + .btn-light-primary,
[data-pc-preset=preset-6] .btn-check:checked + .btn-light-primary {
  background: #fd7e14;
  color: #fff;
  border-color: #fd7e14;
}
[data-pc-preset=preset-6] .btn-link-primary {
  background: transparent;
  color: #fd7e14;
  border-color: transparent;
}
[data-pc-preset=preset-6] .btn-link-primary .material-icons-two-tone {
  background-color: #fd7e14;
}
[data-pc-preset=preset-6] .btn-link-primary:hover {
  background: #fff2e8;
  color: #fd7e14;
  border-color: #fff2e8;
}
[data-pc-preset=preset-6] .btn-link-primary.focus, [data-pc-preset=preset-6] .btn-link-primary:focus {
  background: #fff2e8;
  color: #fd7e14;
  border-color: #fff2e8;
}
[data-pc-preset=preset-6] .btn-link-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-6] .btn-link-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-6] .btn-link-primary.dropdown-toggle {
  background: #fff2e8;
  color: #fd7e14;
  border-color: #fff2e8;
}
[data-pc-preset=preset-6] .btn-check:active + .btn-link-primary,
[data-pc-preset=preset-6] .btn-check:checked + .btn-link-primary {
  background: #fff2e8;
  color: #fd7e14;
  border-color: #fff2e8;
}

[data-pc-preset=preset-7] {
  --pc-sidebar-active-color: #e58a00;
  --bs-blue: #e58a00;
  --bs-primary: #e58a00;
  --bs-primary-rgb: 229, 138, 0;
  --bs-primary-light: #fcf3e6;
  --bs-link-color: #e58a00;
  --bs-link-color-rgb: 229, 138, 0;
  --bs-link-hover-color: #b76e00;
  --bs-link-hover-color-rgb: to-rgb(shift-color($pc-primary, $link-shade-percentage));
  --dt-row-selected: 229, 138, 0;
}
[data-pc-preset=preset-7] .bg-light-primary {
  background: #fcf3e6;
  color: #e58a00;
}
[data-pc-preset=preset-7] .link-primary {
  color: #e58a00 !important;
}
[data-pc-preset=preset-7] .link-primary:hover, [data-pc-preset=preset-7] .link-primary:focus {
  color: #b76e00 !important;
}
[data-pc-preset=preset-7] .btn-primary {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #e58a00;
  --bs-btn-border-color: #e58a00;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #c37500;
  --bs-btn-hover-border-color: #b76e00;
  --bs-btn-focus-shadow-rgb: 233, 156, 38;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #b76e00;
  --bs-btn-active-border-color: #ac6800;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #e58a00;
  --bs-btn-disabled-border-color: #e58a00;
}
[data-pc-preset=preset-7] .btn-link {
  --bs-btn-color: #e58a00;
  --bs-btn-hover-color: #b76e00;
  --bs-btn-active-color: #b76e00;
}
[data-pc-preset=preset-7] .text-bg-primary {
  color: #ffffff !important;
  background-color: RGBA(229, 138, 0, var(--bs-bg-opacity, 1)) !important;
}
[data-pc-preset=preset-7] .accordion {
  --bs-accordion-btn-focus-border-color: #e58a00;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.2rem rgba(229, 138, 0, 0.25);
  --bs-accordion-active-color: #e58a00;
  --bs-accordion-active-bg: #fcf3e6;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23e58a00'%3e%3cpath fill-rule=' evenodd' d=' M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-7] .alert-primary {
  --bs-alert-color: #895300;
  --bs-alert-bg: #fae8cc;
  --bs-alert-border-color: #f7dcb3;
  --bs-alert-link-color: #6e4200;
}
[data-pc-preset=preset-7] .list-group {
  --bs-list-group-active-bg: #e58a00;
  --bs-list-group-active-border-color: #e58a00;
}
[data-pc-preset=preset-7] .list-group-item-primary {
  color: #895300;
  background-color: #fae8cc;
}
[data-pc-preset=preset-7] .nav {
  --bs-nav-link-hover-color: #b76e00;
}
[data-pc-preset=preset-7] .nav-pills {
  --bs-nav-pills-link-active-bg: #e58a00;
}
[data-pc-preset=preset-7] .pagination {
  --bs-pagination-hover-color: #b76e00;
  --bs-pagination-focus-color: #b76e00;
  --bs-pagination-focus-box-shadow: 0 0 0 0.2rem rgba(229, 138, 0, 0.25);
  --bs-pagination-active-bg: #e58a00;
  --bs-pagination-active-border-color: #e58a00;
}
[data-pc-preset=preset-7] .progress {
  --bs-progress-bar-bg: #e58a00;
}
[data-pc-preset=preset-7] .form-check .form-check-input.input-primary:checked {
  border-color: #e58a00;
  background-color: #e58a00;
}
[data-pc-preset=preset-7] .form-check .form-check-input.input-light-primary:checked {
  border-color: #fcf3e6;
  background-color: #fcf3e6;
}
[data-pc-preset=preset-7] .form-check .form-check-input.input-light-primary:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23e58a00' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-7] .form-check .form-check-input.input-light-primary:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23e58a00'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-7] .form-check .form-check-input.input-primary:focus[type=checkbox], [data-pc-preset=preset-7] .form-check .form-check-input.input-primary:focus[type=radio], [data-pc-preset=preset-7] .form-check .form-check-input.input-light-primary:focus[type=checkbox], [data-pc-preset=preset-7] .form-check .form-check-input.input-light-primary:focus[type=radio] {
  box-shadow: 0 0 0 0.2rem rgba(229, 138, 0, 0.25);
  border-color: #e58a00;
}
[data-pc-preset=preset-7] .form-check.form-switch .form-check-input.input-light-primary:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23e58a00'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-7] .btn-light-primary {
  background: #fcf3e6;
  color: #e58a00;
  border-color: #fcf3e6;
}
[data-pc-preset=preset-7] .btn-light-primary .material-icons-two-tone {
  background-color: #e58a00;
}
[data-pc-preset=preset-7] .btn-light-primary:hover {
  background: #e58a00;
  color: #fff;
  border-color: #e58a00;
}
[data-pc-preset=preset-7] .btn-light-primary.focus, [data-pc-preset=preset-7] .btn-light-primary:focus {
  background: #e58a00;
  color: #fff;
  border-color: #e58a00;
}
[data-pc-preset=preset-7] .btn-light-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-7] .btn-light-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-7] .btn-light-primary.dropdown-toggle {
  background: #e58a00;
  color: #fff;
  border-color: #e58a00;
}
[data-pc-preset=preset-7] .btn-check:active + .btn-light-primary,
[data-pc-preset=preset-7] .btn-check:checked + .btn-light-primary {
  background: #e58a00;
  color: #fff;
  border-color: #e58a00;
}
[data-pc-preset=preset-7] .btn-link-primary {
  background: transparent;
  color: #e58a00;
  border-color: transparent;
}
[data-pc-preset=preset-7] .btn-link-primary .material-icons-two-tone {
  background-color: #e58a00;
}
[data-pc-preset=preset-7] .btn-link-primary:hover {
  background: #fcf3e6;
  color: #e58a00;
  border-color: #fcf3e6;
}
[data-pc-preset=preset-7] .btn-link-primary.focus, [data-pc-preset=preset-7] .btn-link-primary:focus {
  background: #fcf3e6;
  color: #e58a00;
  border-color: #fcf3e6;
}
[data-pc-preset=preset-7] .btn-link-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-7] .btn-link-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-7] .btn-link-primary.dropdown-toggle {
  background: #fcf3e6;
  color: #e58a00;
  border-color: #fcf3e6;
}
[data-pc-preset=preset-7] .btn-check:active + .btn-link-primary,
[data-pc-preset=preset-7] .btn-check:checked + .btn-link-primary {
  background: #fcf3e6;
  color: #e58a00;
  border-color: #fcf3e6;
}

[data-pc-preset=preset-8] {
  --pc-sidebar-active-color: #2ca87f;
  --bs-blue: #2ca87f;
  --bs-primary: #2ca87f;
  --bs-primary-rgb: 44, 168, 127;
  --bs-primary-light: #eaf6f2;
  --bs-link-color: #2ca87f;
  --bs-link-color-rgb: 44, 168, 127;
  --bs-link-hover-color: #238666;
  --bs-link-hover-color-rgb: to-rgb(shift-color($pc-primary, $link-shade-percentage));
  --dt-row-selected: 44, 168, 127;
}
[data-pc-preset=preset-8] .bg-light-primary {
  background: #eaf6f2;
  color: #2ca87f;
}
[data-pc-preset=preset-8] .link-primary {
  color: #2ca87f !important;
}
[data-pc-preset=preset-8] .link-primary:hover, [data-pc-preset=preset-8] .link-primary:focus {
  color: #238666 !important;
}
[data-pc-preset=preset-8] .btn-primary {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #2ca87f;
  --bs-btn-border-color: #2ca87f;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #258f6c;
  --bs-btn-hover-border-color: #238666;
  --bs-btn-focus-shadow-rgb: 76, 181, 146;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #238666;
  --bs-btn-active-border-color: #217e5f;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #2ca87f;
  --bs-btn-disabled-border-color: #2ca87f;
}
[data-pc-preset=preset-8] .btn-link {
  --bs-btn-color: #2ca87f;
  --bs-btn-hover-color: #238666;
  --bs-btn-active-color: #238666;
}
[data-pc-preset=preset-8] .text-bg-primary {
  color: #ffffff !important;
  background-color: RGBA(44, 168, 127, var(--bs-bg-opacity, 1)) !important;
}
[data-pc-preset=preset-8] .accordion {
  --bs-accordion-btn-focus-border-color: #2ca87f;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.2rem rgba(44, 168, 127, 0.25);
  --bs-accordion-active-color: #2ca87f;
  --bs-accordion-active-bg: #eaf6f2;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%232ca87f'%3e%3cpath fill-rule=' evenodd' d=' M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-8] .alert-primary {
  --bs-alert-color: #1a654c;
  --bs-alert-bg: #d5eee5;
  --bs-alert-border-color: #c0e5d9;
  --bs-alert-link-color: #15513d;
}
[data-pc-preset=preset-8] .list-group {
  --bs-list-group-active-bg: #2ca87f;
  --bs-list-group-active-border-color: #2ca87f;
}
[data-pc-preset=preset-8] .list-group-item-primary {
  color: #1a654c;
  background-color: #d5eee5;
}
[data-pc-preset=preset-8] .nav {
  --bs-nav-link-hover-color: #238666;
}
[data-pc-preset=preset-8] .nav-pills {
  --bs-nav-pills-link-active-bg: #2ca87f;
}
[data-pc-preset=preset-8] .pagination {
  --bs-pagination-hover-color: #238666;
  --bs-pagination-focus-color: #238666;
  --bs-pagination-focus-box-shadow: 0 0 0 0.2rem rgba(44, 168, 127, 0.25);
  --bs-pagination-active-bg: #2ca87f;
  --bs-pagination-active-border-color: #2ca87f;
}
[data-pc-preset=preset-8] .progress {
  --bs-progress-bar-bg: #2ca87f;
}
[data-pc-preset=preset-8] .form-check .form-check-input.input-primary:checked {
  border-color: #2ca87f;
  background-color: #2ca87f;
}
[data-pc-preset=preset-8] .form-check .form-check-input.input-light-primary:checked {
  border-color: #eaf6f2;
  background-color: #eaf6f2;
}
[data-pc-preset=preset-8] .form-check .form-check-input.input-light-primary:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%232ca87f' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-8] .form-check .form-check-input.input-light-primary:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%232ca87f'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-8] .form-check .form-check-input.input-primary:focus[type=checkbox], [data-pc-preset=preset-8] .form-check .form-check-input.input-primary:focus[type=radio], [data-pc-preset=preset-8] .form-check .form-check-input.input-light-primary:focus[type=checkbox], [data-pc-preset=preset-8] .form-check .form-check-input.input-light-primary:focus[type=radio] {
  box-shadow: 0 0 0 0.2rem rgba(44, 168, 127, 0.25);
  border-color: #2ca87f;
}
[data-pc-preset=preset-8] .form-check.form-switch .form-check-input.input-light-primary:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%232ca87f'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-8] .btn-light-primary {
  background: #eaf6f2;
  color: #2ca87f;
  border-color: #eaf6f2;
}
[data-pc-preset=preset-8] .btn-light-primary .material-icons-two-tone {
  background-color: #2ca87f;
}
[data-pc-preset=preset-8] .btn-light-primary:hover {
  background: #2ca87f;
  color: #fff;
  border-color: #2ca87f;
}
[data-pc-preset=preset-8] .btn-light-primary.focus, [data-pc-preset=preset-8] .btn-light-primary:focus {
  background: #2ca87f;
  color: #fff;
  border-color: #2ca87f;
}
[data-pc-preset=preset-8] .btn-light-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-8] .btn-light-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-8] .btn-light-primary.dropdown-toggle {
  background: #2ca87f;
  color: #fff;
  border-color: #2ca87f;
}
[data-pc-preset=preset-8] .btn-check:active + .btn-light-primary,
[data-pc-preset=preset-8] .btn-check:checked + .btn-light-primary {
  background: #2ca87f;
  color: #fff;
  border-color: #2ca87f;
}
[data-pc-preset=preset-8] .btn-link-primary {
  background: transparent;
  color: #2ca87f;
  border-color: transparent;
}
[data-pc-preset=preset-8] .btn-link-primary .material-icons-two-tone {
  background-color: #2ca87f;
}
[data-pc-preset=preset-8] .btn-link-primary:hover {
  background: #eaf6f2;
  color: #2ca87f;
  border-color: #eaf6f2;
}
[data-pc-preset=preset-8] .btn-link-primary.focus, [data-pc-preset=preset-8] .btn-link-primary:focus {
  background: #eaf6f2;
  color: #2ca87f;
  border-color: #eaf6f2;
}
[data-pc-preset=preset-8] .btn-link-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-8] .btn-link-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-8] .btn-link-primary.dropdown-toggle {
  background: #eaf6f2;
  color: #2ca87f;
  border-color: #eaf6f2;
}
[data-pc-preset=preset-8] .btn-check:active + .btn-link-primary,
[data-pc-preset=preset-8] .btn-check:checked + .btn-link-primary {
  background: #eaf6f2;
  color: #2ca87f;
  border-color: #eaf6f2;
}

[data-pc-preset=preset-9] {
  --pc-sidebar-active-color: #008080;
  --bs-blue: #008080;
  --bs-primary: #008080;
  --bs-primary-rgb: 0, 128, 128;
  --bs-primary-light: #e6f2f2;
  --bs-link-color: #008080;
  --bs-link-color-rgb: 0, 128, 128;
  --bs-link-hover-color: #006666;
  --bs-link-hover-color-rgb: to-rgb(shift-color($pc-primary, $link-shade-percentage));
  --dt-row-selected: 0, 128, 128;
}
[data-pc-preset=preset-9] .bg-light-primary {
  background: #e6f2f2;
  color: #008080;
}
[data-pc-preset=preset-9] .link-primary {
  color: #008080 !important;
}
[data-pc-preset=preset-9] .link-primary:hover, [data-pc-preset=preset-9] .link-primary:focus {
  color: #006666 !important;
}
[data-pc-preset=preset-9] .btn-primary {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #008080;
  --bs-btn-border-color: #008080;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #006d6d;
  --bs-btn-hover-border-color: #006666;
  --bs-btn-focus-shadow-rgb: 38, 147, 147;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #006666;
  --bs-btn-active-border-color: #006060;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #008080;
  --bs-btn-disabled-border-color: #008080;
}
[data-pc-preset=preset-9] .btn-link {
  --bs-btn-color: #008080;
  --bs-btn-hover-color: #006666;
  --bs-btn-active-color: #006666;
}
[data-pc-preset=preset-9] .text-bg-primary {
  color: #ffffff !important;
  background-color: RGBA(0, 128, 128, var(--bs-bg-opacity, 1)) !important;
}
[data-pc-preset=preset-9] .accordion {
  --bs-accordion-btn-focus-border-color: #008080;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.2rem rgba(0, 128, 128, 0.25);
  --bs-accordion-active-color: #008080;
  --bs-accordion-active-bg: #e6f2f2;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23008080'%3e%3cpath fill-rule=' evenodd' d=' M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-9] .alert-primary {
  --bs-alert-color: #004d4d;
  --bs-alert-bg: #cce6e6;
  --bs-alert-border-color: #b3d9d9;
  --bs-alert-link-color: #003e3e;
}
[data-pc-preset=preset-9] .list-group {
  --bs-list-group-active-bg: #008080;
  --bs-list-group-active-border-color: #008080;
}
[data-pc-preset=preset-9] .list-group-item-primary {
  color: #004d4d;
  background-color: #cce6e6;
}
[data-pc-preset=preset-9] .nav {
  --bs-nav-link-hover-color: #006666;
}
[data-pc-preset=preset-9] .nav-pills {
  --bs-nav-pills-link-active-bg: #008080;
}
[data-pc-preset=preset-9] .pagination {
  --bs-pagination-hover-color: #006666;
  --bs-pagination-focus-color: #006666;
  --bs-pagination-focus-box-shadow: 0 0 0 0.2rem rgba(0, 128, 128, 0.25);
  --bs-pagination-active-bg: #008080;
  --bs-pagination-active-border-color: #008080;
}
[data-pc-preset=preset-9] .progress {
  --bs-progress-bar-bg: #008080;
}
[data-pc-preset=preset-9] .form-check .form-check-input.input-primary:checked {
  border-color: #008080;
  background-color: #008080;
}
[data-pc-preset=preset-9] .form-check .form-check-input.input-light-primary:checked {
  border-color: #e6f2f2;
  background-color: #e6f2f2;
}
[data-pc-preset=preset-9] .form-check .form-check-input.input-light-primary:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23008080' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-9] .form-check .form-check-input.input-light-primary:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23008080'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-9] .form-check .form-check-input.input-primary:focus[type=checkbox], [data-pc-preset=preset-9] .form-check .form-check-input.input-primary:focus[type=radio], [data-pc-preset=preset-9] .form-check .form-check-input.input-light-primary:focus[type=checkbox], [data-pc-preset=preset-9] .form-check .form-check-input.input-light-primary:focus[type=radio] {
  box-shadow: 0 0 0 0.2rem rgba(0, 128, 128, 0.25);
  border-color: #008080;
}
[data-pc-preset=preset-9] .form-check.form-switch .form-check-input.input-light-primary:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23008080'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-9] .btn-light-primary {
  background: #e6f2f2;
  color: #008080;
  border-color: #e6f2f2;
}
[data-pc-preset=preset-9] .btn-light-primary .material-icons-two-tone {
  background-color: #008080;
}
[data-pc-preset=preset-9] .btn-light-primary:hover {
  background: #008080;
  color: #fff;
  border-color: #008080;
}
[data-pc-preset=preset-9] .btn-light-primary.focus, [data-pc-preset=preset-9] .btn-light-primary:focus {
  background: #008080;
  color: #fff;
  border-color: #008080;
}
[data-pc-preset=preset-9] .btn-light-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-9] .btn-light-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-9] .btn-light-primary.dropdown-toggle {
  background: #008080;
  color: #fff;
  border-color: #008080;
}
[data-pc-preset=preset-9] .btn-check:active + .btn-light-primary,
[data-pc-preset=preset-9] .btn-check:checked + .btn-light-primary {
  background: #008080;
  color: #fff;
  border-color: #008080;
}
[data-pc-preset=preset-9] .btn-link-primary {
  background: transparent;
  color: #008080;
  border-color: transparent;
}
[data-pc-preset=preset-9] .btn-link-primary .material-icons-two-tone {
  background-color: #008080;
}
[data-pc-preset=preset-9] .btn-link-primary:hover {
  background: #e6f2f2;
  color: #008080;
  border-color: #e6f2f2;
}
[data-pc-preset=preset-9] .btn-link-primary.focus, [data-pc-preset=preset-9] .btn-link-primary:focus {
  background: #e6f2f2;
  color: #008080;
  border-color: #e6f2f2;
}
[data-pc-preset=preset-9] .btn-link-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-9] .btn-link-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-9] .btn-link-primary.dropdown-toggle {
  background: #e6f2f2;
  color: #008080;
  border-color: #e6f2f2;
}
[data-pc-preset=preset-9] .btn-check:active + .btn-link-primary,
[data-pc-preset=preset-9] .btn-check:checked + .btn-link-primary {
  background: #e6f2f2;
  color: #008080;
  border-color: #e6f2f2;
}

[data-pc-preset=preset-10] {
  --pc-sidebar-active-color: #3ec9d6;
  --bs-blue: #3ec9d6;
  --bs-primary: #3ec9d6;
  --bs-primary-rgb: 62, 201, 214;
  --bs-primary-light: #ecfafb;
  --bs-link-color: #3ec9d6;
  --bs-link-color-rgb: 62, 201, 214;
  --bs-link-hover-color: #32a1ab;
  --bs-link-hover-color-rgb: to-rgb(shift-color($pc-primary, $link-shade-percentage));
  --dt-row-selected: 62, 201, 214;
}
[data-pc-preset=preset-10] .bg-light-primary {
  background: #ecfafb;
  color: #3ec9d6;
}
[data-pc-preset=preset-10] .link-primary {
  color: #3ec9d6 !important;
}
[data-pc-preset=preset-10] .link-primary:hover, [data-pc-preset=preset-10] .link-primary:focus {
  color: #32a1ab !important;
}
[data-pc-preset=preset-10] .btn-primary {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #3ec9d6;
  --bs-btn-border-color: #3ec9d6;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #35abb6;
  --bs-btn-hover-border-color: #32a1ab;
  --bs-btn-focus-shadow-rgb: 91, 209, 220;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #32a1ab;
  --bs-btn-active-border-color: #2f97a1;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #3ec9d6;
  --bs-btn-disabled-border-color: #3ec9d6;
}
[data-pc-preset=preset-10] .btn-link {
  --bs-btn-color: #3ec9d6;
  --bs-btn-hover-color: #32a1ab;
  --bs-btn-active-color: #32a1ab;
}
[data-pc-preset=preset-10] .text-bg-primary {
  color: #ffffff !important;
  background-color: RGBA(62, 201, 214, var(--bs-bg-opacity, 1)) !important;
}
[data-pc-preset=preset-10] .accordion {
  --bs-accordion-btn-focus-border-color: #3ec9d6;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.2rem rgba(62, 201, 214, 0.25);
  --bs-accordion-active-color: #3ec9d6;
  --bs-accordion-active-bg: #ecfafb;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%233ec9d6'%3e%3cpath fill-rule=' evenodd' d=' M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-10] .alert-primary {
  --bs-alert-color: #257980;
  --bs-alert-bg: #d8f4f7;
  --bs-alert-border-color: #c5eff3;
  --bs-alert-link-color: #1e6166;
}
[data-pc-preset=preset-10] .list-group {
  --bs-list-group-active-bg: #3ec9d6;
  --bs-list-group-active-border-color: #3ec9d6;
}
[data-pc-preset=preset-10] .list-group-item-primary {
  color: #257980;
  background-color: #d8f4f7;
}
[data-pc-preset=preset-10] .nav {
  --bs-nav-link-hover-color: #32a1ab;
}
[data-pc-preset=preset-10] .nav-pills {
  --bs-nav-pills-link-active-bg: #3ec9d6;
}
[data-pc-preset=preset-10] .pagination {
  --bs-pagination-hover-color: #32a1ab;
  --bs-pagination-focus-color: #32a1ab;
  --bs-pagination-focus-box-shadow: 0 0 0 0.2rem rgba(62, 201, 214, 0.25);
  --bs-pagination-active-bg: #3ec9d6;
  --bs-pagination-active-border-color: #3ec9d6;
}
[data-pc-preset=preset-10] .progress {
  --bs-progress-bar-bg: #3ec9d6;
}
[data-pc-preset=preset-10] .form-check .form-check-input.input-primary:checked {
  border-color: #3ec9d6;
  background-color: #3ec9d6;
}
[data-pc-preset=preset-10] .form-check .form-check-input.input-light-primary:checked {
  border-color: #ecfafb;
  background-color: #ecfafb;
}
[data-pc-preset=preset-10] .form-check .form-check-input.input-light-primary:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%233ec9d6' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-10] .form-check .form-check-input.input-light-primary:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%233ec9d6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-10] .form-check .form-check-input.input-primary:focus[type=checkbox], [data-pc-preset=preset-10] .form-check .form-check-input.input-primary:focus[type=radio], [data-pc-preset=preset-10] .form-check .form-check-input.input-light-primary:focus[type=checkbox], [data-pc-preset=preset-10] .form-check .form-check-input.input-light-primary:focus[type=radio] {
  box-shadow: 0 0 0 0.2rem rgba(62, 201, 214, 0.25);
  border-color: #3ec9d6;
}
[data-pc-preset=preset-10] .form-check.form-switch .form-check-input.input-light-primary:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%233ec9d6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-10] .btn-light-primary {
  background: #ecfafb;
  color: #3ec9d6;
  border-color: #ecfafb;
}
[data-pc-preset=preset-10] .btn-light-primary .material-icons-two-tone {
  background-color: #3ec9d6;
}
[data-pc-preset=preset-10] .btn-light-primary:hover {
  background: #3ec9d6;
  color: #fff;
  border-color: #3ec9d6;
}
[data-pc-preset=preset-10] .btn-light-primary.focus, [data-pc-preset=preset-10] .btn-light-primary:focus {
  background: #3ec9d6;
  color: #fff;
  border-color: #3ec9d6;
}
[data-pc-preset=preset-10] .btn-light-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-10] .btn-light-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-10] .btn-light-primary.dropdown-toggle {
  background: #3ec9d6;
  color: #fff;
  border-color: #3ec9d6;
}
[data-pc-preset=preset-10] .btn-check:active + .btn-light-primary,
[data-pc-preset=preset-10] .btn-check:checked + .btn-light-primary {
  background: #3ec9d6;
  color: #fff;
  border-color: #3ec9d6;
}
[data-pc-preset=preset-10] .btn-link-primary {
  background: transparent;
  color: #3ec9d6;
  border-color: transparent;
}
[data-pc-preset=preset-10] .btn-link-primary .material-icons-two-tone {
  background-color: #3ec9d6;
}
[data-pc-preset=preset-10] .btn-link-primary:hover {
  background: #ecfafb;
  color: #3ec9d6;
  border-color: #ecfafb;
}
[data-pc-preset=preset-10] .btn-link-primary.focus, [data-pc-preset=preset-10] .btn-link-primary:focus {
  background: #ecfafb;
  color: #3ec9d6;
  border-color: #ecfafb;
}
[data-pc-preset=preset-10] .btn-link-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-10] .btn-link-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-10] .btn-link-primary.dropdown-toggle {
  background: #ecfafb;
  color: #3ec9d6;
  border-color: #ecfafb;
}
[data-pc-preset=preset-10] .btn-check:active + .btn-link-primary,
[data-pc-preset=preset-10] .btn-check:checked + .btn-link-primary {
  background: #ecfafb;
  color: #3ec9d6;
  border-color: #ecfafb;
}

[data-pc-preset=preset-11] {
  --pc-sidebar-active-color: #131920;
  --bs-blue: #131920;
  --bs-primary: #131920;
  --bs-primary-rgb: 19, 25, 32;
  --bs-primary-light: #e7e8e9;
  --bs-link-color: #131920;
  --bs-link-color-rgb: 19, 25, 32;
  --bs-link-hover-color: #0f141a;
  --bs-link-hover-color-rgb: to-rgb(shift-color($pc-primary, $link-shade-percentage));
  --dt-row-selected: 19, 25, 32;
}
[data-pc-preset=preset-11] .bg-light-primary {
  background: #e7e8e9;
  color: #131920;
}
[data-pc-preset=preset-11] .link-primary {
  color: #131920 !important;
}
[data-pc-preset=preset-11] .link-primary:hover, [data-pc-preset=preset-11] .link-primary:focus {
  color: #0f141a !important;
}
[data-pc-preset=preset-11] .btn-primary {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #131920;
  --bs-btn-border-color: #131920;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #10151b;
  --bs-btn-hover-border-color: #0f141a;
  --bs-btn-focus-shadow-rgb: 54, 60, 65;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #0f141a;
  --bs-btn-active-border-color: #0e1318;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #131920;
  --bs-btn-disabled-border-color: #131920;
}
[data-pc-preset=preset-11] .btn-link {
  --bs-btn-color: #131920;
  --bs-btn-hover-color: #0f141a;
  --bs-btn-active-color: #0f141a;
}
[data-pc-preset=preset-11] .text-bg-primary {
  color: #ffffff !important;
  background-color: RGBA(19, 25, 32, var(--bs-bg-opacity, 1)) !important;
}
[data-pc-preset=preset-11] .accordion {
  --bs-accordion-btn-focus-border-color: #131920;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.2rem rgba(19, 25, 32, 0.25);
  --bs-accordion-active-color: #131920;
  --bs-accordion-active-bg: #e7e8e9;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23131920'%3e%3cpath fill-rule=' evenodd' d=' M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-11] .alert-primary {
  --bs-alert-color: #0b0f13;
  --bs-alert-bg: #d0d1d2;
  --bs-alert-border-color: #b8babc;
  --bs-alert-link-color: #090c0f;
}
[data-pc-preset=preset-11] .list-group {
  --bs-list-group-active-bg: #131920;
  --bs-list-group-active-border-color: #131920;
}
[data-pc-preset=preset-11] .list-group-item-primary {
  color: #0b0f13;
  background-color: #d0d1d2;
}
[data-pc-preset=preset-11] .nav {
  --bs-nav-link-hover-color: #0f141a;
}
[data-pc-preset=preset-11] .nav-pills {
  --bs-nav-pills-link-active-bg: #131920;
}
[data-pc-preset=preset-11] .pagination {
  --bs-pagination-hover-color: #0f141a;
  --bs-pagination-focus-color: #0f141a;
  --bs-pagination-focus-box-shadow: 0 0 0 0.2rem rgba(19, 25, 32, 0.25);
  --bs-pagination-active-bg: #131920;
  --bs-pagination-active-border-color: #131920;
}
[data-pc-preset=preset-11] .progress {
  --bs-progress-bar-bg: #131920;
}
[data-pc-preset=preset-11] .form-check .form-check-input.input-primary:checked {
  border-color: #131920;
  background-color: #131920;
}
[data-pc-preset=preset-11] .form-check .form-check-input.input-light-primary:checked {
  border-color: #e7e8e9;
  background-color: #e7e8e9;
}
[data-pc-preset=preset-11] .form-check .form-check-input.input-light-primary:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23131920' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-11] .form-check .form-check-input.input-light-primary:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23131920'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-11] .form-check .form-check-input.input-primary:focus[type=checkbox], [data-pc-preset=preset-11] .form-check .form-check-input.input-primary:focus[type=radio], [data-pc-preset=preset-11] .form-check .form-check-input.input-light-primary:focus[type=checkbox], [data-pc-preset=preset-11] .form-check .form-check-input.input-light-primary:focus[type=radio] {
  box-shadow: 0 0 0 0.2rem rgba(19, 25, 32, 0.25);
  border-color: #131920;
}
[data-pc-preset=preset-11] .form-check.form-switch .form-check-input.input-light-primary:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23131920'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-11] .btn-light-primary {
  background: #e7e8e9;
  color: #131920;
  border-color: #e7e8e9;
}
[data-pc-preset=preset-11] .btn-light-primary .material-icons-two-tone {
  background-color: #131920;
}
[data-pc-preset=preset-11] .btn-light-primary:hover {
  background: #131920;
  color: #fff;
  border-color: #131920;
}
[data-pc-preset=preset-11] .btn-light-primary.focus, [data-pc-preset=preset-11] .btn-light-primary:focus {
  background: #131920;
  color: #fff;
  border-color: #131920;
}
[data-pc-preset=preset-11] .btn-light-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-11] .btn-light-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-11] .btn-light-primary.dropdown-toggle {
  background: #131920;
  color: #fff;
  border-color: #131920;
}
[data-pc-preset=preset-11] .btn-check:active + .btn-light-primary,
[data-pc-preset=preset-11] .btn-check:checked + .btn-light-primary {
  background: #131920;
  color: #fff;
  border-color: #131920;
}
[data-pc-preset=preset-11] .btn-link-primary {
  background: transparent;
  color: #131920;
  border-color: transparent;
}
[data-pc-preset=preset-11] .btn-link-primary .material-icons-two-tone {
  background-color: #131920;
}
[data-pc-preset=preset-11] .btn-link-primary:hover {
  background: #e7e8e9;
  color: #131920;
  border-color: #e7e8e9;
}
[data-pc-preset=preset-11] .btn-link-primary.focus, [data-pc-preset=preset-11] .btn-link-primary:focus {
  background: #e7e8e9;
  color: #131920;
  border-color: #e7e8e9;
}
[data-pc-preset=preset-11] .btn-link-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-11] .btn-link-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-11] .btn-link-primary.dropdown-toggle {
  background: #e7e8e9;
  color: #131920;
  border-color: #e7e8e9;
}
[data-pc-preset=preset-11] .btn-check:active + .btn-link-primary,
[data-pc-preset=preset-11] .btn-check:checked + .btn-link-primary {
  background: #e7e8e9;
  color: #131920;
  border-color: #e7e8e9;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
