name: Vue deploy

# Controls when the action will run.
on:
  # Triggers the workflow on push or pull request events but only for the master branch
  push:
    branches:
      - master
    paths:
      - 'vue/**'

jobs:
  #if_merged:
  #  if: github.event.pull_request.merged == true
  Vue-Deploy:
    name: 🎉 Vue Deploy
    runs-on: ubuntu-latest

    steps:
      - name: 🚚 Get latest code
        uses: actions/checkout@v4

      - name: Use Node.js 20
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: 🔨 Build Project
        run: |
          cd vue
          npm i
          npm run build-prod

      - name: 📂 Deploy to Server
        uses: easingthemes/ssh-deploy@v4
        env:
          SSH_PRIVATE_KEY: ${{ secrets.SERVER_SSH_KEY }}
          # ARGS: "-rltgoDzvO --delete"
          SOURCE: "vue/dist/"
          REMOTE_HOST: *************
          REMOTE_USER: ableproadmin
          TARGET: public_html/vue/free
          EXCLUDE: "/full-version/node_modules/"
