{"name": "able-pro-bootstrap", "version": "2.5.0", "description": "Able Pro Bootstrap 5 Admin Template", "main": "index.js", "author": {"name": "Phoenixcoded", "email": "<EMAIL>", "url": "https://themeforest.net/user/phoenixcoded"}, "scripts": {"start": "gulp", "build": "gulp build-prod", "format": "prettier --write ./src"}, "overrides": {"graceful-fs": "^4.2.11"}, "devDependencies": {"@babel/core": "^7.24.3", "@babel/helper-environment-visitor": "^7.22.20", "del": "^6.1.1", "gulp": "^4.0.2", "gulp-autoprefixer": "^6.1.0", "gulp-babel": "^8.0.0", "gulp-cssbeautify": "^3.0.1", "gulp-cssmin": "^0.2.0", "gulp-inject": "^5.0.5", "gulp-minify": "^3.1.0", "gulp-sass": "^5.1.0", "gulp-smushit": "1.2.0", "gulp-uglify": "^3.0.2", "merge-stream": "^2.0.0", "prettier": "3.2.5", "sass": "1.71.1"}, "dependencies": {"@popperjs/core": "^2.11.8", "apexcharts": "^3.54.0", "bootstrap": "5.3.3", "browser-sync": "^3.0.2", "clipboard": "^2.0.11", "feather-icons": "^4.29.0", "gulp-file-include": "^2.3.0", "gulp-htmlmin": "^5.0.1", "gulp-sourcemaps": "^3.0.0", "simplebar": "^6.2.5"}}