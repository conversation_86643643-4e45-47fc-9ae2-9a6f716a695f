{"name": "@vue/shared", "version": "3.5.15", "description": "internal utils shared across @vue packages", "main": "index.js", "module": "dist/shared.esm-bundler.js", "types": "dist/shared.d.ts", "files": ["index.js", "dist"], "exports": {".": {"types": "./dist/shared.d.ts", "node": {"production": "./dist/shared.cjs.prod.js", "development": "./dist/shared.cjs.js", "default": "./index.js"}, "module": "./dist/shared.esm-bundler.js", "import": "./dist/shared.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "sideEffects": false, "buildOptions": {"formats": ["esm-bundler", "cjs"]}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/shared"}, "keywords": ["vue"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/core/issues"}, "homepage": "https://github.com/vuejs/core/tree/main/packages/shared#readme"}