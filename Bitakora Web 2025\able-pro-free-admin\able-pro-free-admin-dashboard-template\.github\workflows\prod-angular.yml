name: Ang<PERSON> deploy

# Controls when the action will run.
on:
  # Triggers the workflow on push or pull request events but only for the master branch
  push:
    branches:
      - master
    paths:
      - 'angular/**'
  #pull_request:
  #  types:
  #    - closed
  #  branches:
  #    - master

jobs:
  #if_merged:
  #  if: github.event.pull_request.merged == true
  Angular-Deploy:
    name: 🎉 Angular Deploy
    runs-on: ubuntu-latest

    steps:
      - name: 🚚 Get latest code
        uses: actions/checkout@v3

      - name: Install Node.js 18
        uses: actions/setup-node@v3
        with:
          node-version: "18.x"

      - name: 🔨 Build Project
        run: |
          cd angular
          npm install --legacy-peer-deps
          npm run build

      - name: 📂 Deploy to Server
        uses: easingthemes/ssh-deploy@main
        env:
          SSH_PRIVATE_KEY: ${{ secrets.SERVER_SSH_KEY }}
          # ARGS: "-rltgoDzvO --delete"
          SOURCE: "angular/dist/"
          REMOTE_HOST: *************
          REMOTE_USER: ableproadmin
          TARGET: public_html/angular/free
          EXCLUDE: "/angular/node_modules/"
