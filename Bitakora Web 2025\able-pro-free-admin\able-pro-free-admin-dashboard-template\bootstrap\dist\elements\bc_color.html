<!DOCTYPE html>
<html lang="en">
  <!-- [Head] start -->
  <head>
    <title>Color | Able Pro Dashboard Template</title>
    <!-- [Meta] -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="description" content="Able Pro is trending dashboard template made using Bootstrap 5 design framework. Able Pro is available in Bootstrap, React, CodeIgniter, Angular,  and .net Technologies.">
    <meta name="keywords" content="Bootstrap admin template, Dashboard UI Kit, Dashboard Template, Backend Panel, react dashboard, angular dashboard">
    <meta name="author" content="Phoenixcoded">

    <!-- [Favicon] icon -->
    <link rel="icon" href="../assets/images/favicon.svg" type="image/x-icon"> <!-- [Font] Family -->
<link rel="stylesheet" href="../assets/fonts/inter/inter.css" id="main-font-link" />
<!-- [Tabler Icons] https://tablericons.com -->
<link rel="stylesheet" href="../assets/fonts/tabler-icons.min.css" >
<!-- [Feather Icons] https://feathericons.com -->
<link rel="stylesheet" href="../assets/fonts/feather.css" >
<!-- [Font Awesome Icons] https://fontawesome.com/icons -->
<link rel="stylesheet" href="../assets/fonts/fontawesome.css" >
<!-- [Material Icons] https://fonts.google.com/icons -->
<link rel="stylesheet" href="../assets/fonts/material.css" >
<!-- [Template CSS Files] -->
<link rel="stylesheet" href="../assets/css/style.css" id="main-style-link" >
<link rel="stylesheet" href="../assets/css/style-preset.css" >
  </head>
  <!-- [Head] end -->
  <!-- [Body] Start -->
  <body data-pc-preset="preset-1" data-pc-sidebar-caption="true" data-pc-layout="vertical" data-pc-direction="ltr" data-pc-theme_contrast="" data-pc-theme="light">
    <!-- [ Pre-loader ] start -->
<div class="loader-bg">
  <div class="loader-track">
    <div class="loader-fill"></div>
  </div>
</div>
<!-- [ Pre-loader ] End -->
 <!-- [ Sidebar Menu ] start -->
<nav class="pc-sidebar">
  <div class="navbar-wrapper">
    <div class="m-header">
      <a href="../dashboard/index.html" class="b-brand text-primary">
        <!-- ========   Change your logo from here   ============ -->
        <img src="../assets/images/logo-dark.svg" class="img-fluid logo-lg" alt="logo">
        <span class="badge bg-light-success rounded-pill ms-2 theme-version">v2.5.0</span>
      </a>
    </div>
    <div class="navbar-content">
      <div class="card pc-user-card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="flex-shrink-0">
              <img src="../assets/images/user/avatar-1.jpg" alt="user-image" class="user-avtar wid-45 rounded-circle" />
            </div>
            <div class="flex-grow-1 ms-3 me-2">
              <h6 class="mb-0">Jonh Smith</h6>
              <small>Administrator</small>
            </div>
            <a class="btn btn-icon btn-link-secondary avtar" data-bs-toggle="collapse" href="#pc_sidebar_userlink">
              <svg class="pc-icon">
                <use xlink:href="#custom-sort-outline"></use>
              </svg>
            </a>
          </div>
          <div class="collapse pc-user-links" id="pc_sidebar_userlink">
            <div class="pt-3">
              <a href="#!">
                <i class="ti ti-user"></i>
                <span>My Account</span>
              </a>
              <a href="#!">
                <i class="ti ti-settings"></i>
                <span>Settings</span>
              </a>
              <a href="#!">
                <i class="ti ti-lock"></i>
                <span>Lock Screen</span>
              </a>
              <a href="#!">
                <i class="ti ti-power"></i>
                <span>Logout</span>
              </a>
            </div>
          </div>
        </div>
      </div>

      <ul class="pc-navbar">
        <li class="pc-item pc-caption">
          <label>Navigation</label>
        </li>

        <li class="pc-item">
          <a href="../dashboard/index.html" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-status-up"></use>
              </svg>
            </span>
            <span class="pc-mtext">Dashboard</span>
          </a>
        </li>

        <li class="pc-item pc-caption">
          <label>Authentication</label>
        </li>
        <li class="pc-item">
          <a href="../pages/login-v1.html" class="pc-link" target="_blank"
            ><span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-shield"></use>
              </svg> </span
            ><span class="pc-mtext">Login</span></a
          >
        </li>
        <li class="pc-item">
          <a href="../pages/register-v1.html" class="pc-link" target="_blank"
            ><span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-password-check"></use>
              </svg> </span
            ><span class="pc-mtext">Register</span></a
          >
        </li>
        <li class="pc-item pc-caption">
          <label>UI Components</label>
          <svg class="pc-icon">
            <use xlink:href="#custom-box-1"></use>
          </svg>
        </li>
        <li class="pc-item">
          <a href="../elements/bc_typography.html" class="pc-link"
            ><span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-text-block"></use>
              </svg> </span
            ><span class="pc-mtext">Typography</span></a
          >
        </li>
        <li class="pc-item">
          <a href="../elements/bc_color.html" class="pc-link"
            ><span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-clipboard"></use>
              </svg> </span
            ><span class="pc-mtext">Color</span></a
          >
        </li>
        <li class="pc-item">
          <a href="../elements/icon-tabler.html" class="pc-link"
            ><span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-mouse-circle"></use>
              </svg> </span
            ><span class="pc-mtext">Tabler</span>
          </a>
        </li>
        <li class="pc-item pc-caption">
          <label>Other</label>
          <svg class="pc-icon">
            <use xlink:href="#custom-notification-status"></use>
          </svg>
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link"
            ><span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-level"></use>
              </svg> </span
            ><span class="pc-mtext">Menu levels</span><span class="pc-arrow"><i data-feather="chevron-right"></i></span
          ></a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="#!">Level 2.1</a></li>
            <li class="pc-item pc-hasmenu">
              <a href="#!" class="pc-link"
                >Level 2.2<span class="pc-arrow"><i data-feather="chevron-right"></i></span
              ></a>
              <ul class="pc-submenu">
                <li class="pc-item"><a class="pc-link" href="#!">Level 3.1</a></li>
                <li class="pc-item"><a class="pc-link" href="#!">Level 3.2</a></li>
                <li class="pc-item pc-hasmenu">
                  <a href="#!" class="pc-link"
                    >Level 3.3<span class="pc-arrow"><i data-feather="chevron-right"></i></span
                  ></a>
                  <ul class="pc-submenu">
                    <li class="pc-item"><a class="pc-link" href="#!">Level 4.1</a></li>
                    <li class="pc-item"><a class="pc-link" href="#!">Level 4.2</a></li>
                  </ul>
                </li>
              </ul>
            </li>
            <li class="pc-item pc-hasmenu">
              <a href="#!" class="pc-link"
                >Level 2.3<span class="pc-arrow"><i data-feather="chevron-right"></i></span
              ></a>
              <ul class="pc-submenu">
                <li class="pc-item"><a class="pc-link" href="#!">Level 3.1</a></li>
                <li class="pc-item"><a class="pc-link" href="#!">Level 3.2</a></li>
                <li class="pc-item pc-hasmenu">
                  <a href="#!" class="pc-link"
                    >Level 3.3<span class="pc-arrow"><i data-feather="chevron-right"></i></span
                  ></a>
                  <ul class="pc-submenu">
                    <li class="pc-item"><a class="pc-link" href="#!">Level 4.1</a></li>
                    <li class="pc-item"><a class="pc-link" href="#!">Level 4.2</a></li>
                  </ul>
                </li>
              </ul>
            </li>
          </ul>
        </li>
        <li class="pc-item"
          ><a href="../other/sample-page.html" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-notification-status"></use>
              </svg>
            </span>
            <span class="pc-mtext">Sample page</span></a
          ></li
        >

      </ul>
      <div class="card pc-user-card mt-3">
        <div class="card-body text-center">
          <img src="../assets/images/application/img-coupon.png" alt="img" class="img-fluid w-50" />
          <h5 class="mb-0 mt-1">Able Pro</h5>
          <p>Checkout pro features</p>
          <a
            href="https://themeforest.net/item/able-pro-bootstrap-admin-dashboard-template/50170229"
            target="_blank"
            class="btn btn-warning">
            <svg class="pc-icon me-2">
              <use xlink:href="#custom-logout-1-outline"></use>
            </svg>
            Upgrade to Pro
          </a>
        </div>
      </div>
    </div>
  </div>
</nav>
<!-- [ Sidebar Menu ] end --> <!-- [ Header Topbar ] start -->
<header class="pc-header">
  <div class="header-wrapper"> <!-- [Mobile Media Block] start -->
<div class="me-auto pc-mob-drp">
  <ul class="list-unstyled">
    <!-- ======= Menu collapse Icon ===== -->
    <li class="pc-h-item pc-sidebar-collapse">
      <a href="#" class="pc-head-link ms-0" id="sidebar-hide">
        <i class="ti ti-menu-2"></i>
      </a>
    </li>
    <li class="pc-h-item pc-sidebar-popup">
      <a href="#" class="pc-head-link ms-0" id="mobile-collapse">
        <i class="ti ti-menu-2"></i>
      </a>
    </li>
    <li class="dropdown pc-h-item">
      <a
        class="pc-head-link dropdown-toggle arrow-none m-0 trig-drp-search"
        data-bs-toggle="dropdown"
        href="#"
        role="button"
        aria-haspopup="false"
        aria-expanded="false"
      >
        <svg class="pc-icon">
          <use xlink:href="#custom-search-normal-1"></use>
        </svg>
      </a>
      <div class="dropdown-menu pc-h-dropdown drp-search">
        <form class="px-3 py-2">
          <input type="search" class="form-control border-0 shadow-none" placeholder="Search here. . ." />
        </form>
      </div>
    </li>
  </ul>
</div>
<!-- [Mobile Media Block end] -->
<div class="ms-auto">
  <ul class="list-unstyled">
    <li class="dropdown pc-h-item">
      <a
        class="pc-head-link dropdown-toggle arrow-none me-0"
        data-bs-toggle="dropdown"
        href="#"
        role="button"
        aria-haspopup="false"
        aria-expanded="false"
      >
        <svg class="pc-icon">
          <use xlink:href="#custom-notification"></use>
        </svg>
        <span class="badge bg-success pc-h-badge">3</span>
      </a>
      <div class="dropdown-menu dropdown-notification dropdown-menu-end pc-h-dropdown">
        <div class="dropdown-header d-flex align-items-center justify-content-between">
          <h5 class="m-0">Notifications</h5>
          <a href="#!" class="btn btn-link btn-sm">Mark all read</a>
        </div>
        <div class="dropdown-body text-wrap header-notification-scroll position-relative" style="max-height: calc(100vh - 215px)">
          <p class="text-span">Today</p>
          <div class="card mb-2">
            <div class="card-body">
              <div class="d-flex">
                <div class="flex-shrink-0">
                  <svg class="pc-icon text-primary">
                    <use xlink:href="#custom-layer"></use>
                  </svg>
                </div>
                <div class="flex-grow-1 ms-3">
                  <span class="float-end text-sm text-muted">2 min ago</span>
                  <h5 class="text-body mb-2">UI/UX Design</h5>
                  <p class="mb-0"
                    >Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of
                    type and scrambled it to make a type</p
                  >
                </div>
              </div>
            </div>
          </div>
          <div class="card mb-2">
            <div class="card-body">
              <div class="d-flex">
                <div class="flex-shrink-0">
                  <svg class="pc-icon text-primary">
                    <use xlink:href="#custom-sms"></use>
                  </svg>
                </div>
                <div class="flex-grow-1 ms-3">
                  <span class="float-end text-sm text-muted">1 hour ago</span>
                  <h5 class="text-body mb-2">Message</h5>
                  <p class="mb-0">Lorem Ipsum has been the industry's standard dummy text ever since the 1500.</p>
                </div>
              </div>
            </div>
          </div>
          <p class="text-span">Yesterday</p>
          <div class="card mb-2">
            <div class="card-body">
              <div class="d-flex">
                <div class="flex-shrink-0">
                  <svg class="pc-icon text-primary">
                    <use xlink:href="#custom-document-text"></use>
                  </svg>
                </div>
                <div class="flex-grow-1 ms-3">
                  <span class="float-end text-sm text-muted">2 hour ago</span>
                  <h5 class="text-body mb-2">Forms</h5>
                  <p class="mb-0"
                    >Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of
                    type and scrambled it to make a type</p
                  >
                </div>
              </div>
            </div>
          </div>
          <div class="card mb-2">
            <div class="card-body">
              <div class="d-flex">
                <div class="flex-shrink-0">
                  <svg class="pc-icon text-primary">
                    <use xlink:href="#custom-user-bold"></use>
                  </svg>
                </div>
                <div class="flex-grow-1 ms-3">
                  <span class="float-end text-sm text-muted">12 hour ago</span>
                  <h5 class="text-body mb-2">Challenge invitation</h5>
                  <p class="mb-2"><span class="text-dark">Jonny aber</span> invites to join the challenge</p>
                  <button class="btn btn-sm btn-outline-secondary me-2">Decline</button>
                  <button class="btn btn-sm btn-primary">Accept</button>
                </div>
              </div>
            </div>
          </div>
          <div class="card mb-2">
            <div class="card-body">
              <div class="d-flex">
                <div class="flex-shrink-0">
                  <svg class="pc-icon text-primary">
                    <use xlink:href="#custom-security-safe"></use>
                  </svg>
                </div>
                <div class="flex-grow-1 ms-3">
                  <span class="float-end text-sm text-muted">5 hour ago</span>
                  <h5 class="text-body mb-2">Security</h5>
                  <p class="mb-0"
                    >Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of
                    type and scrambled it to make a type</p
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="text-center py-2">
          <a href="#!" class="link-danger">Clear all Notifications</a>
        </div>
      </div>
    </li>
    <li class="dropdown pc-h-item header-user-profile">
      <a
        class="pc-head-link dropdown-toggle arrow-none me-0"
        data-bs-toggle="dropdown"
        href="#"
        role="button"
        aria-haspopup="false"
        data-bs-auto-close="outside"
        aria-expanded="false"
      >
        <img src="../assets/images/user/avatar-2.jpg" alt="user-image" class="user-avtar" />
      </a>
      <div class="dropdown-menu dropdown-user-profile dropdown-menu-end pc-h-dropdown">
        <div class="dropdown-header d-flex align-items-center justify-content-between">
          <h5 class="m-0">Profile</h5>
        </div>
        <div class="dropdown-body">
          <div class="profile-notification-scroll position-relative" style="max-height: calc(100vh - 225px)">
            <div class="d-flex mb-1">
              <div class="flex-shrink-0">
                <img src="../assets/images/user/avatar-2.jpg" alt="user-image" class="user-avtar wid-35" />
              </div>
              <div class="flex-grow-1 ms-3">
                <h6 class="mb-1">Carson Darrin 🖖</h6>
                <span><EMAIL></span>
              </div>
            </div>
            <hr class="border-secondary border-opacity-50" />
            <div class="card">
              <div class="card-body py-3">
                <div class="d-flex align-items-center justify-content-between">
                  <h5 class="mb-0 d-inline-flex align-items-center"
                    ><svg class="pc-icon text-muted me-2">
                      <use xlink:href="#custom-notification-outline"></use></svg
                    >Notification</h5
                  >
                  <div class="form-check form-switch form-check-reverse m-0">
                    <input class="form-check-input f-18" type="checkbox" role="switch" />
                  </div>
                </div>
              </div>
            </div>
            <p class="text-span">Manage</p>
            <a href="#" class="dropdown-item">
              <span>
                <svg class="pc-icon text-muted me-2">
                  <use xlink:href="#custom-setting-outline"></use>
                </svg>
                <span>Settings</span>
              </span>
            </a>
            <a href="#" class="dropdown-item">
              <span>
                <svg class="pc-icon text-muted me-2">
                  <use xlink:href="#custom-share-bold"></use>
                </svg>
                <span>Share</span>
              </span>
            </a>
            <a href="#" class="dropdown-item">
              <span>
                <svg class="pc-icon text-muted me-2">
                  <use xlink:href="#custom-lock-outline"></use>
                </svg>
                <span>Change Password</span>
              </span>
            </a>
            <hr class="border-secondary border-opacity-50" />
            <p class="text-span">Team</p>
            <a href="#" class="dropdown-item">
              <span>
                <svg class="pc-icon text-muted me-2">
                  <use xlink:href="#custom-profile-2user-outline"></use>
                </svg>
                <span>UI Design team</span>
              </span>
              <div class="user-group">
                <img src="../assets/images/user/avatar-1.jpg" alt="user-image" class="avtar" />
                <span class="avtar bg-danger text-white">K</span>
                <span class="avtar bg-success text-white">
                  <svg class="pc-icon m-0">
                    <use xlink:href="#custom-user"></use>
                  </svg>
                </span>
                <span class="avtar bg-light-primary text-primary">+2</span>
              </div>
            </a>
            <a href="#" class="dropdown-item">
              <span>
                <svg class="pc-icon text-muted me-2">
                  <use xlink:href="#custom-profile-2user-outline"></use>
                </svg>
                <span>Friends Groups</span>
              </span>
              <div class="user-group">
                <img src="../assets/images/user/avatar-1.jpg" alt="user-image" class="avtar" />
                <span class="avtar bg-danger text-white">K</span>
                <span class="avtar bg-success text-white">
                  <svg class="pc-icon m-0">
                    <use xlink:href="#custom-user"></use>
                  </svg>
                </span>
              </div>
            </a>
            <a href="#" class="dropdown-item">
              <span>
                <svg class="pc-icon text-muted me-2">
                  <use xlink:href="#custom-add-outline"></use>
                </svg>
                <span>Add new</span>
              </span>
              <div class="user-group">
                <span class="avtar bg-primary text-white">
                  <svg class="pc-icon m-0">
                    <use xlink:href="#custom-add-outline"></use>
                  </svg>
                </span>
              </div>
            </a>
            <hr class="border-secondary border-opacity-50" />
            <div class="d-grid mb-3">
              <button class="btn btn-primary">
                <svg class="pc-icon me-2">
                  <use xlink:href="#custom-logout-1-outline"></use></svg
                >Logout
              </button>
            </div>
            <div
              class="card border-0 shadow-none drp-upgrade-card mb-0"
              style="background-image: url(../assets/images/layout/img-profile-card.jpg)"
            >
              <div class="card-body">
                <div class="user-group">
                  <img src="../assets/images/user/avatar-1.jpg" alt="user-image" class="avtar" />
                  <img src="../assets/images/user/avatar-2.jpg" alt="user-image" class="avtar" />
                  <img src="../assets/images/user/avatar-3.jpg" alt="user-image" class="avtar" />
                  <img src="../assets/images/user/avatar-4.jpg" alt="user-image" class="avtar" />
                  <img src="../assets/images/user/avatar-5.jpg" alt="user-image" class="avtar" />
                  <span class="avtar bg-light-primary text-primary">+20</span>
                </div>
                <h3 class="my-3 text-dark">245.3k <small class="text-muted">Followers</small></h3>
                <div class="btn btn btn-warning">
                  <svg class="pc-icon me-2">
                    <use xlink:href="#custom-logout-1-outline"></use>
                  </svg>
                  Upgrade to Business
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </li>
  </ul>
</div>
 </div>
</header>
<!-- [ Header ] end -->



    <!--[ Main Content ] start-->
    <div class="pc-container">
      <div class="pc-content">
        <!-- [ breadcrumb ] start -->
        <div class="page-header">
          <div class="page-block">
            <div class="row align-items-center">
              <div class="col-md-12">
                <ul class="breadcrumb">
                  <li class="breadcrumb-item"><a href="../dashboard/index.html">Home</a></li>
                  <li class="breadcrumb-item"><a href="javascript: void(0)">UI Components</a></li>
                  <li class="breadcrumb-item" aria-current="page">Color</li>
                </ul>
              </div>
              <div class="col-md-12">
                <div class="page-header-title">
                  <h2 class="mb-0">Color</h2>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- [ breadcrumb ] end -->

        <!--[ Main Content ] start-->
        <div class="row">
          <!-- [ link-button ] start -->
          <div class="col-sm-12">
            <div class="card">
              <div class="card-header">
                <h5>Background Color</h5>
              </div>
              <div class="card-body">
                <div class="row gy-4">
                  <div class="col-md-4">
                    <div class="p-3 color-block bg-blue-100" data-clipboard-text="bg-blue-100">bg-blue-100</div>
                    <div class="p-3 color-block bg-blue-200" data-clipboard-text="bg-blue-200">bg-blue-200</div>
                    <div class="p-3 color-block bg-blue-300" data-clipboard-text="bg-blue-300">bg-blue-300</div>
                    <div class="p-3 color-block bg-blue-400" data-clipboard-text="bg-blue-400">bg-blue-400</div>
                    <div class="p-3 color-block bg-blue-500" data-clipboard-text="bg-blue-500">bg-blue-500</div>
                    <div class="p-3 color-block bg-blue-600" data-clipboard-text="bg-blue-600">bg-blue-600</div>
                    <div class="p-3 color-block bg-blue-700" data-clipboard-text="bg-blue-700">bg-blue-700</div>
                    <div class="p-3 color-block bg-blue-800" data-clipboard-text="bg-blue-800">bg-blue-800</div>
                    <div class="p-3 color-block bg-blue-900" data-clipboard-text="bg-blue-900">bg-blue-900</div>
                  </div>
                  <div class="col-md-4">
                    <div class="p-3 color-block bg-indigo-100" data-clipboard-text="bg-indigo-100">bg-indigo-100</div>
                    <div class="p-3 color-block bg-indigo-200" data-clipboard-text="bg-indigo-200">bg-indigo-200</div>
                    <div class="p-3 color-block bg-indigo-300" data-clipboard-text="bg-indigo-300">bg-indigo-300</div>
                    <div class="p-3 color-block bg-indigo-400" data-clipboard-text="bg-indigo-400">bg-indigo-400</div>
                    <div class="p-3 color-block bg-indigo-500" data-clipboard-text="bg-indigo-500">bg-indigo-500</div>
                    <div class="p-3 color-block bg-indigo-600" data-clipboard-text="bg-indigo-600">bg-indigo-600</div>
                    <div class="p-3 color-block bg-indigo-700" data-clipboard-text="bg-indigo-700">bg-indigo-700</div>
                    <div class="p-3 color-block bg-indigo-800" data-clipboard-text="bg-indigo-800">bg-indigo-800</div>
                    <div class="p-3 color-block bg-indigo-900" data-clipboard-text="bg-indigo-900">bg-indigo-900</div>
                  </div>
                  <div class="col-md-4">
                    <div class="p-3 color-block bg-purple-100" data-clipboard-text="bg-purple-100">bg-purple-100</div>
                    <div class="p-3 color-block bg-purple-200" data-clipboard-text="bg-purple-200">bg-purple-200</div>
                    <div class="p-3 color-block bg-purple-300" data-clipboard-text="bg-purple-300">bg-purple-300</div>
                    <div class="p-3 color-block bg-purple-400" data-clipboard-text="bg-purple-400">bg-purple-400</div>
                    <div class="p-3 color-block bg-purple-500" data-clipboard-text="bg-purple-500">bg-purple-500</div>
                    <div class="p-3 color-block bg-purple-600" data-clipboard-text="bg-purple-600">bg-purple-600</div>
                    <div class="p-3 color-block bg-purple-700" data-clipboard-text="bg-purple-700">bg-purple-700</div>
                    <div class="p-3 color-block bg-purple-800" data-clipboard-text="bg-purple-800">bg-purple-800</div>
                    <div class="p-3 color-block bg-purple-900" data-clipboard-text="bg-purple-900">bg-purple-900</div>
                  </div>
                  <div class="col-md-4">
                    <div class="p-3 color-block bg-pink-100" data-clipboard-text="bg-pink-100">bg-pink-100</div>
                    <div class="p-3 color-block bg-pink-200" data-clipboard-text="bg-pink-200">bg-pink-200</div>
                    <div class="p-3 color-block bg-pink-300" data-clipboard-text="bg-pink-300">bg-pink-300</div>
                    <div class="p-3 color-block bg-pink-400" data-clipboard-text="bg-pink-400">bg-pink-400</div>
                    <div class="p-3 color-block bg-pink-500" data-clipboard-text="bg-pink-500">bg-pink-500</div>
                    <div class="p-3 color-block bg-pink-600" data-clipboard-text="bg-pink-600">bg-pink-600</div>
                    <div class="p-3 color-block bg-pink-700" data-clipboard-text="bg-pink-700">bg-pink-700</div>
                    <div class="p-3 color-block bg-pink-800" data-clipboard-text="bg-pink-800">bg-pink-800</div>
                    <div class="p-3 color-block bg-pink-900" data-clipboard-text="bg-pink-900">bg-pink-900</div>
                  </div>
                  <div class="col-md-4">
                    <div class="p-3 color-block bg-red-100" data-clipboard-text="bg-red-100">bg-red-100</div>
                    <div class="p-3 color-block bg-red-200" data-clipboard-text="bg-red-200">bg-red-200</div>
                    <div class="p-3 color-block bg-red-300" data-clipboard-text="bg-red-300">bg-red-300</div>
                    <div class="p-3 color-block bg-red-400" data-clipboard-text="bg-red-400">bg-red-400</div>
                    <div class="p-3 color-block bg-red-500" data-clipboard-text="bg-red-500">bg-red-500</div>
                    <div class="p-3 color-block bg-red-600" data-clipboard-text="bg-red-600">bg-red-600</div>
                    <div class="p-3 color-block bg-red-700" data-clipboard-text="bg-red-700">bg-red-700</div>
                    <div class="p-3 color-block bg-red-800" data-clipboard-text="bg-red-800">bg-red-800</div>
                    <div class="p-3 color-block bg-red-900" data-clipboard-text="bg-red-900">bg-red-900</div>
                  </div>
                  <div class="col-md-4">
                    <div class="p-3 color-block bg-orange-100" data-clipboard-text="bg-orange-100">bg-orange-100</div>
                    <div class="p-3 color-block bg-orange-200" data-clipboard-text="bg-orange-200">bg-orange-200</div>
                    <div class="p-3 color-block bg-orange-300" data-clipboard-text="bg-orange-300">bg-orange-300</div>
                    <div class="p-3 color-block bg-orange-400" data-clipboard-text="bg-orange-400">bg-orange-400</div>
                    <div class="p-3 color-block bg-orange-500" data-clipboard-text="bg-orange-500">bg-orange-500</div>
                    <div class="p-3 color-block bg-orange-600" data-clipboard-text="bg-orange-600">bg-orange-600</div>
                    <div class="p-3 color-block bg-orange-700" data-clipboard-text="bg-orange-700">bg-orange-700</div>
                    <div class="p-3 color-block bg-orange-800" data-clipboard-text="bg-orange-800">bg-orange-800</div>
                    <div class="p-3 color-block bg-orange-900" data-clipboard-text="bg-orange-900">bg-orange-900</div>
                  </div>
                  <div class="col-md-4">
                    <div class="p-3 color-block bg-yellow-100" data-clipboard-text="bg-yellow-100">bg-yellow-100</div>
                    <div class="p-3 color-block bg-yellow-200" data-clipboard-text="bg-yellow-200">bg-yellow-200</div>
                    <div class="p-3 color-block bg-yellow-300" data-clipboard-text="bg-yellow-300">bg-yellow-300</div>
                    <div class="p-3 color-block bg-yellow-400" data-clipboard-text="bg-yellow-400">bg-yellow-400</div>
                    <div class="p-3 color-block bg-yellow-500" data-clipboard-text="bg-yellow-500">bg-yellow-500</div>
                    <div class="p-3 color-block bg-yellow-600" data-clipboard-text="bg-yellow-600">bg-yellow-600</div>
                    <div class="p-3 color-block bg-yellow-700" data-clipboard-text="bg-yellow-700">bg-yellow-700</div>
                    <div class="p-3 color-block bg-yellow-800" data-clipboard-text="bg-yellow-800">bg-yellow-800</div>
                    <div class="p-3 color-block bg-yellow-900" data-clipboard-text="bg-yellow-900">bg-yellow-900</div>
                  </div>
                  <div class="col-md-4">
                    <div class="p-3 color-block bg-green-100" data-clipboard-text="bg-green-100">bg-green-100</div>
                    <div class="p-3 color-block bg-green-200" data-clipboard-text="bg-green-200">bg-green-200</div>
                    <div class="p-3 color-block bg-green-300" data-clipboard-text="bg-green-300">bg-green-300</div>
                    <div class="p-3 color-block bg-green-400" data-clipboard-text="bg-green-400">bg-green-400</div>
                    <div class="p-3 color-block bg-green-500" data-clipboard-text="bg-green-500">bg-green-500</div>
                    <div class="p-3 color-block bg-green-600" data-clipboard-text="bg-green-600">bg-green-600</div>
                    <div class="p-3 color-block bg-green-700" data-clipboard-text="bg-green-700">bg-green-700</div>
                    <div class="p-3 color-block bg-green-800" data-clipboard-text="bg-green-800">bg-green-800</div>
                    <div class="p-3 color-block bg-green-900" data-clipboard-text="bg-green-900">bg-green-900</div>
                  </div>
                  <div class="col-md-4">
                    <div class="p-3 color-block bg-teal-100" data-clipboard-text="bg-teal-100">bg-teal-100</div>
                    <div class="p-3 color-block bg-teal-200" data-clipboard-text="bg-teal-200">bg-teal-200</div>
                    <div class="p-3 color-block bg-teal-300" data-clipboard-text="bg-teal-300">bg-teal-300</div>
                    <div class="p-3 color-block bg-teal-400" data-clipboard-text="bg-teal-400">bg-teal-400</div>
                    <div class="p-3 color-block bg-teal-500" data-clipboard-text="bg-teal-500">bg-teal-500</div>
                    <div class="p-3 color-block bg-teal-600" data-clipboard-text="bg-teal-600">bg-teal-600</div>
                    <div class="p-3 color-block bg-teal-700" data-clipboard-text="bg-teal-700">bg-teal-700</div>
                    <div class="p-3 color-block bg-teal-800" data-clipboard-text="bg-teal-800">bg-teal-800</div>
                    <div class="p-3 color-block bg-teal-900" data-clipboard-text="bg-teal-900">bg-teal-900</div>
                  </div>
                  <div class="col-md-4">
                    <div class="p-3 color-block bg-cyan-100" data-clipboard-text="bg-cyan-100">bg-cyan-100</div>
                    <div class="p-3 color-block bg-cyan-200" data-clipboard-text="bg-cyan-200">bg-cyan-200</div>
                    <div class="p-3 color-block bg-cyan-300" data-clipboard-text="bg-cyan-300">bg-cyan-300</div>
                    <div class="p-3 color-block bg-cyan-400" data-clipboard-text="bg-cyan-400">bg-cyan-400</div>
                    <div class="p-3 color-block bg-cyan-500" data-clipboard-text="bg-cyan-500">bg-cyan-500</div>
                    <div class="p-3 color-block bg-cyan-600" data-clipboard-text="bg-cyan-600">bg-cyan-600</div>
                    <div class="p-3 color-block bg-cyan-700" data-clipboard-text="bg-cyan-700">bg-cyan-700</div>
                    <div class="p-3 color-block bg-cyan-800" data-clipboard-text="bg-cyan-800">bg-cyan-800</div>
                    <div class="p-3 color-block bg-cyan-900" data-clipboard-text="bg-cyan-900">bg-cyan-900</div>
                  </div>
                  <div class="col-md-4">
                    <div class="p-3 color-block bg-gray-100" data-clipboard-text="bg-gray-100">bg-gray-100</div>
                    <div class="p-3 color-block bg-gray-200" data-clipboard-text="bg-gray-200">bg-gray-200</div>
                    <div class="p-3 color-block bg-gray-300" data-clipboard-text="bg-gray-300">bg-gray-300</div>
                    <div class="p-3 color-block bg-gray-400" data-clipboard-text="bg-gray-400">bg-gray-400</div>
                    <div class="p-3 color-block bg-gray-500" data-clipboard-text="bg-gray-500">bg-gray-500</div>
                    <div class="p-3 color-block bg-gray-600" data-clipboard-text="bg-gray-600">bg-gray-600</div>
                    <div class="p-3 color-block bg-gray-700" data-clipboard-text="bg-gray-700">bg-gray-700</div>
                    <div class="p-3 color-block bg-gray-800" data-clipboard-text="bg-gray-800">bg-gray-800</div>
                    <div class="p-3 color-block bg-gray-900" data-clipboard-text="bg-gray-900">bg-gray-900</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="card color-card">
              <div class="card-header">
                <h5>Text Color</h5>
              </div>
              <div class="card-body">
                <div class="row gy-4">
                  <div class="col-md-4">
                    <div class="p-3 color-block text-blue-100" data-clipboard-text="text-blue-100">text-blue-100</div>
                    <div class="p-3 color-block text-blue-200" data-clipboard-text="text-blue-200">text-blue-200</div>
                    <div class="p-3 color-block text-blue-300" data-clipboard-text="text-blue-300">text-blue-300</div>
                    <div class="p-3 color-block text-blue-400" data-clipboard-text="text-blue-400">text-blue-400</div>
                    <div class="p-3 color-block text-blue-500" data-clipboard-text="text-blue-500">text-blue-500</div>
                    <div class="p-3 color-block text-blue-600" data-clipboard-text="text-blue-600">text-blue-600</div>
                    <div class="p-3 color-block text-blue-700" data-clipboard-text="text-blue-700">text-blue-700</div>
                    <div class="p-3 color-block text-blue-800" data-clipboard-text="text-blue-800">text-blue-800</div>
                    <div class="p-3 color-block text-blue-900" data-clipboard-text="text-blue-900">text-blue-900</div>
                  </div>
                  <div class="col-md-4">
                    <div class="p-3 color-block text-indigo-100" data-clipboard-text="text-indigo-100">text-indigo-100 </div>
                    <div class="p-3 color-block text-indigo-200" data-clipboard-text="text-indigo-200">text-indigo-200 </div>
                    <div class="p-3 color-block text-indigo-300" data-clipboard-text="text-indigo-300">text-indigo-300 </div>
                    <div class="p-3 color-block text-indigo-400" data-clipboard-text="text-indigo-400">text-indigo-400 </div>
                    <div class="p-3 color-block text-indigo-500" data-clipboard-text="text-indigo-500">text-indigo-500 </div>
                    <div class="p-3 color-block text-indigo-600" data-clipboard-text="text-indigo-600">text-indigo-600 </div>
                    <div class="p-3 color-block text-indigo-700" data-clipboard-text="text-indigo-700">text-indigo-700 </div>
                    <div class="p-3 color-block text-indigo-800" data-clipboard-text="text-indigo-800">text-indigo-800 </div>
                    <div class="p-3 color-block text-indigo-900" data-clipboard-text="text-indigo-900">text-indigo-900 </div>
                  </div>
                  <div class="col-md-4">
                    <div class="p-3 color-block text-purple-100" data-clipboard-text="text-purple-100">text-purple-100 </div>
                    <div class="p-3 color-block text-purple-200" data-clipboard-text="text-purple-200">text-purple-200 </div>
                    <div class="p-3 color-block text-purple-300" data-clipboard-text="text-purple-300">text-purple-300 </div>
                    <div class="p-3 color-block text-purple-400" data-clipboard-text="text-purple-400">text-purple-400 </div>
                    <div class="p-3 color-block text-purple-500" data-clipboard-text="text-purple-500">text-purple-500 </div>
                    <div class="p-3 color-block text-purple-600" data-clipboard-text="text-purple-600">text-purple-600 </div>
                    <div class="p-3 color-block text-purple-700" data-clipboard-text="text-purple-700">text-purple-700 </div>
                    <div class="p-3 color-block text-purple-800" data-clipboard-text="text-purple-800">text-purple-800 </div>
                    <div class="p-3 color-block text-purple-900" data-clipboard-text="text-purple-900">text-purple-900 </div>
                  </div>
                  <div class="col-md-4">
                    <div class="p-3 color-block text-pink-100" data-clipboard-text="text-pink-100">text-pink-100</div>
                    <div class="p-3 color-block text-pink-200" data-clipboard-text="text-pink-200">text-pink-200</div>
                    <div class="p-3 color-block text-pink-300" data-clipboard-text="text-pink-300">text-pink-300</div>
                    <div class="p-3 color-block text-pink-400" data-clipboard-text="text-pink-400">text-pink-400</div>
                    <div class="p-3 color-block text-pink-500" data-clipboard-text="text-pink-500">text-pink-500</div>
                    <div class="p-3 color-block text-pink-600" data-clipboard-text="text-pink-600">text-pink-600</div>
                    <div class="p-3 color-block text-pink-700" data-clipboard-text="text-pink-700">text-pink-700</div>
                    <div class="p-3 color-block text-pink-800" data-clipboard-text="text-pink-800">text-pink-800</div>
                    <div class="p-3 color-block text-pink-900" data-clipboard-text="text-pink-900">text-pink-900</div>
                  </div>
                  <div class="col-md-4">
                    <div class="p-3 color-block text-red-100" data-clipboard-text="text-red-100">text-red-100</div>
                    <div class="p-3 color-block text-red-200" data-clipboard-text="text-red-200">text-red-200</div>
                    <div class="p-3 color-block text-red-300" data-clipboard-text="text-red-300">text-red-300</div>
                    <div class="p-3 color-block text-red-400" data-clipboard-text="text-red-400">text-red-400</div>
                    <div class="p-3 color-block text-red-500" data-clipboard-text="text-red-500">text-red-500</div>
                    <div class="p-3 color-block text-red-600" data-clipboard-text="text-red-600">text-red-600</div>
                    <div class="p-3 color-block text-red-700" data-clipboard-text="text-red-700">text-red-700</div>
                    <div class="p-3 color-block text-red-800" data-clipboard-text="text-red-800">text-red-800</div>
                    <div class="p-3 color-block text-red-900" data-clipboard-text="text-red-900">text-red-900</div>
                  </div>
                  <div class="col-md-4">
                    <div class="p-3 color-block text-orange-100" data-clipboard-text="text-orange-100">text-orange-100 </div>
                    <div class="p-3 color-block text-orange-200" data-clipboard-text="text-orange-200">text-orange-200 </div>
                    <div class="p-3 color-block text-orange-300" data-clipboard-text="text-orange-300">text-orange-300 </div>
                    <div class="p-3 color-block text-orange-400" data-clipboard-text="text-orange-400">text-orange-400 </div>
                    <div class="p-3 color-block text-orange-500" data-clipboard-text="text-orange-500">text-orange-500 </div>
                    <div class="p-3 color-block text-orange-600" data-clipboard-text="text-orange-600">text-orange-600 </div>
                    <div class="p-3 color-block text-orange-700" data-clipboard-text="text-orange-700">text-orange-700 </div>
                    <div class="p-3 color-block text-orange-800" data-clipboard-text="text-orange-800">text-orange-800 </div>
                    <div class="p-3 color-block text-orange-900" data-clipboard-text="text-orange-900">text-orange-900 </div>
                  </div>
                  <div class="col-md-4">
                    <div class="p-3 color-block text-yellow-100" data-clipboard-text="text-yellow-100">text-yellow-100 </div>
                    <div class="p-3 color-block text-yellow-200" data-clipboard-text="text-yellow-200">text-yellow-200 </div>
                    <div class="p-3 color-block text-yellow-300" data-clipboard-text="text-yellow-300">text-yellow-300 </div>
                    <div class="p-3 color-block text-yellow-400" data-clipboard-text="text-yellow-400">text-yellow-400 </div>
                    <div class="p-3 color-block text-yellow-500" data-clipboard-text="text-yellow-500">text-yellow-500 </div>
                    <div class="p-3 color-block text-yellow-600" data-clipboard-text="text-yellow-600">text-yellow-600 </div>
                    <div class="p-3 color-block text-yellow-700" data-clipboard-text="text-yellow-700">text-yellow-700 </div>
                    <div class="p-3 color-block text-yellow-800" data-clipboard-text="text-yellow-800">text-yellow-800 </div>
                    <div class="p-3 color-block text-yellow-900" data-clipboard-text="text-yellow-900">text-yellow-900 </div>
                  </div>
                  <div class="col-md-4">
                    <div class="p-3 color-block text-green-100" data-clipboard-text="text-green-100">text-green-100 </div>
                    <div class="p-3 color-block text-green-200" data-clipboard-text="text-green-200">text-green-200 </div>
                    <div class="p-3 color-block text-green-300" data-clipboard-text="text-green-300">text-green-300 </div>
                    <div class="p-3 color-block text-green-400" data-clipboard-text="text-green-400">text-green-400 </div>
                    <div class="p-3 color-block text-green-500" data-clipboard-text="text-green-500">text-green-500 </div>
                    <div class="p-3 color-block text-green-600" data-clipboard-text="text-green-600">text-green-600 </div>
                    <div class="p-3 color-block text-green-700" data-clipboard-text="text-green-700">text-green-700 </div>
                    <div class="p-3 color-block text-green-800" data-clipboard-text="text-green-800">text-green-800 </div>
                    <div class="p-3 color-block text-green-900" data-clipboard-text="text-green-900">text-green-900 </div>
                  </div>
                  <div class="col-md-4">
                    <div class="p-3 color-block text-teal-100" data-clipboard-text="text-teal-100">text-teal-100</div>
                    <div class="p-3 color-block text-teal-200" data-clipboard-text="text-teal-200">text-teal-200</div>
                    <div class="p-3 color-block text-teal-300" data-clipboard-text="text-teal-300">text-teal-300</div>
                    <div class="p-3 color-block text-teal-400" data-clipboard-text="text-teal-400">text-teal-400</div>
                    <div class="p-3 color-block text-teal-500" data-clipboard-text="text-teal-500">text-teal-500</div>
                    <div class="p-3 color-block text-teal-600" data-clipboard-text="text-teal-600">text-teal-600</div>
                    <div class="p-3 color-block text-teal-700" data-clipboard-text="text-teal-700">text-teal-700</div>
                    <div class="p-3 color-block text-teal-800" data-clipboard-text="text-teal-800">text-teal-800</div>
                    <div class="p-3 color-block text-teal-900" data-clipboard-text="text-teal-900">text-teal-900</div>
                  </div>
                  <div class="col-md-4">
                    <div class="p-3 color-block text-cyan-100" data-clipboard-text="text-cyan-100">text-cyan-100</div>
                    <div class="p-3 color-block text-cyan-200" data-clipboard-text="text-cyan-200">text-cyan-200</div>
                    <div class="p-3 color-block text-cyan-300" data-clipboard-text="text-cyan-300">text-cyan-300</div>
                    <div class="p-3 color-block text-cyan-400" data-clipboard-text="text-cyan-400">text-cyan-400</div>
                    <div class="p-3 color-block text-cyan-500" data-clipboard-text="text-cyan-500">text-cyan-500</div>
                    <div class="p-3 color-block text-cyan-600" data-clipboard-text="text-cyan-600">text-cyan-600</div>
                    <div class="p-3 color-block text-cyan-700" data-clipboard-text="text-cyan-700">text-cyan-700</div>
                    <div class="p-3 color-block text-cyan-800" data-clipboard-text="text-cyan-800">text-cyan-800</div>
                    <div class="p-3 color-block text-cyan-900" data-clipboard-text="text-cyan-900">text-cyan-900</div>
                  </div>
                  <div class="col-md-4">
                    <div class="p-3 color-block text-gray-100" data-clipboard-text="text-gray-100">text-gray-100</div>
                    <div class="p-3 color-block text-gray-200" data-clipboard-text="text-gray-200">text-gray-200</div>
                    <div class="p-3 color-block text-gray-300" data-clipboard-text="text-gray-300">text-gray-300</div>
                    <div class="p-3 color-block text-gray-400" data-clipboard-text="text-gray-400">text-gray-400</div>
                    <div class="p-3 color-block text-gray-500" data-clipboard-text="text-gray-500">text-gray-500</div>
                    <div class="p-3 color-block text-gray-600" data-clipboard-text="text-gray-600">text-gray-600</div>
                    <div class="p-3 color-block text-gray-700" data-clipboard-text="text-gray-700">text-gray-700</div>
                    <div class="p-3 color-block text-gray-800" data-clipboard-text="text-gray-800">text-gray-800</div>
                    <div class="p-3 color-block text-gray-900" data-clipboard-text="text-gray-900">text-gray-900</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- [ link-button ] end -->
        </div>
        <!--[ Main Content ] end-->
      </div>
    </div>
    <!--[ Main Content ] end-->
    <footer class="pc-footer">
      <div class="footer-wrapper container-fluid">
        <div class="row">
          <div class="col my-1">
            <p class="m-0"
              >Able Pro &#9829; crafted by Team <a href="https://themeforest.net/user/phoenixcoded" target="_blank">Phoenixcoded</a></p
            >
          </div>
          <div class="col-auto my-1">
            <ul class="list-inline footer-link mb-0">
              <li class="list-inline-item"><a href="../index.html">Home</a></li>
            </ul>
          </div>
        </div>
      </div>
    </footer> <!-- Required Js -->
<script src="../assets/js/plugins/popper.min.js"></script>
<script src="../assets/js/plugins/simplebar.min.js"></script>
<script src="../assets/js/plugins/bootstrap.min.js"></script>
<script src="../assets/js/fonts/custom-font.js"></script>
<script src="../assets/js/script.js"></script>
<script src="../assets/js/theme.js"></script>
<script src="../assets/js/plugins/feather.min.js"></script>










<script>change_box_container('false');</script>


<script>layout_caption_change('true');</script>




<script>layout_rtl_change('false');</script>


<script>preset_change("preset-1");</script>
 

    <!-- [Page Specific JS] start -->
    <script src="../assets/js/plugins/clipboard.min.js"></script>
    <script>
      window.addEventListener('load', (event) => {
        var i_copy = new ClipboardJS('.color-block');
        i_copy.on('success', function (e) {
          var targetElement = e.trigger;
          let icon_badge = document.createElement('span');
          icon_badge.setAttribute('class', 'ic-badge badge bg-success float-end');
          icon_badge.innerHTML = 'copied';
          targetElement.append(icon_badge);
          setTimeout(function () {
            targetElement.children[0].remove();
          }, 3000);
        });

        i_copy.on('error', function (e) {
          var targetElement = e.trigger;
          let icon_badge = document.createElement('span');
          icon_badge.setAttribute('class', 'ic-badge badge bg-danger float-end');
          icon_badge.innerHTML = 'Error';
          targetElement.append(icon_badge);
          setTimeout(function () {
            targetElement.children[0].remove();
          }, 3000);
        });
      });
    </script>
    <!-- [Page Specific JS] end -->
  </body>
  <!-- [Body] end -->
</html>