<div class="row p-t-25">
  <div class="col-md-6">
    <app-card cardTitle="Basic">
      <h1>Inter</h1>
      <h5>Font Family</h5>
      <nav class="m-t-5">
        <ol class="flex align-item-center font-size text-muted">
          <li>
            <h6 class="m-b-0">Regular</h6>
          </li>
          <li class="flex separate">/</li>
          <li>
            <h6 class="m-b-0">Medium</h6>
          </li>
          <li class="flex separate">/</li>
          <li>
            <h6 class="m-b-0">Bold</h6>
          </li>
        </ol>
      </nav>
    </app-card>

    <app-card cardTitle="Heading">
      <div class="m-b-15">
        <h1>H1 Heading</h1>
        <nav class="m-t-15">
          <ol class="flex align-item-center font-size text-muted">
            <li>
              <h6 class="m-b-0">Size: 38px</h6>
            </li>
            <li class="flex separate">/</li>
            <li>
              <h6 class="m-b-0">Weight: Bold</h6>
            </li>
            <li class="flex separate">/</li>
            <li>
              <h6 class="m-b-0">Line Height: 46px</h6>
            </li>
          </ol>
        </nav>
      </div>
      <mat-divider></mat-divider>
      <div class="m-b-15 m-t-15">
        <h2>H2 Heading</h2>
        <nav class="m-t-15">
          <ol class="flex align-item-center font-size text-muted">
            <li>
              <h6 class="m-b-0">Size: 30px</h6>
            </li>
            <li class="flex separate">/</li>
            <li>
              <h6 class="m-b-0">Weight: Bold</h6>
            </li>
            <li class="flex separate">/</li>
            <li>
              <h6 class="m-b-0">Line Height: 38px</h6>
            </li>
          </ol>
        </nav>
      </div>
      <mat-divider></mat-divider>
      <div class="m-b-15 m-t-15">
        <h3>H3 Heading</h3>
        <nav class="m-t-15">
          <ol class="flex align-item-center font-size text-muted">
            <li>
              <h6 class="m-b-0">Size: 24px</h6>
            </li>
            <li class="flex separate">/</li>
            <li>
              <h6 class="m-b-0">Weight: Regular & Bold</h6>
            </li>
            <li class="flex separate">/</li>
            <li>
              <h6 class="m-b-0">Line Height: 32px</h6>
            </li>
          </ol>
        </nav>
      </div>
      <mat-divider></mat-divider>
      <div class="m-b-15 m-t-15">
        <h4>H4 Heading</h4>
        <nav class="m-t-15">
          <ol class="flex align-item-center font-size text-muted">
            <li>
              <h6 class="m-b-0">Size: 20px</h6>
            </li>
            <li class="flex separate">/</li>
            <li>
              <h6 class="m-b-0">Weight: Bold</h6>
            </li>
            <li class="flex separate">/</li>
            <li>
              <h6 class="m-b-0">Line Height: 28px</h6>
            </li>
          </ol>
        </nav>
      </div>
      <mat-divider></mat-divider>
      <div class="m-b-15 m-t-15">
        <h5>H5 Heading</h5>
        <nav class="m-t-15">
          <ol class="flex align-item-center font-size text-muted">
            <li>
              <h6 class="m-b-0">Size: 16px</h6>
            </li>
            <li class="flex separate">/</li>
            <li>
              <h6 class="m-b-0">Weight: Regular & Medium & Bold</h6>
            </li>
            <li class="flex separate">/</li>
            <li>
              <h6 class="m-b-0">Line Height: 24px</h6>
            </li>
          </ol>
        </nav>
      </div>
      <mat-divider></mat-divider>
      <div class="m-b-15 m-t-15">
        <h6>H6 Heading / Subheading</h6>
        <nav class="m-t-15">
          <ol class="flex align-item-center font-size text-muted">
            <li>
              <h6 class="m-b-0">Size: 14px</h6>
            </li>
            <li class="flex separate">/</li>
            <li>
              <h6 class="m-b-0">Weight: Regular</h6>
            </li>
            <li class="flex separate">/</li>
            <li>
              <h6 class="m-b-0">Line Height: 22px</h6>
            </li>
          </ol>
        </nav>
      </div>
    </app-card>
  </div>
  <div class="col-md-6">
    <app-card cardTitle="Alignment">
      <p class="mat-body-2" style="text-align: start">
        Lorem ipsum dolor sit amet consectetur adipisicing elit. Ducimus, obcaecati quasi ab inventore odit non vel corrupti? Non unde
        dolore ipsum possimus pariatur repellat corporis impedit vero, quam, facilis nostrum.
      </p>

      <p class="mat-body-2" style="text-align: center">
        Lorem ipsum dolor sit amet consectetur adipisicing elit. Ducimus, obcaecati quasi ab inventore odit non vel corrupti? Non unde
        dolore ipsum possimus pariatur repellat corporis impedit vero, quam, facilis nostrum.
      </p>
      <p class="mat-body-2" style="text-align: end">
        Lorem ipsum dolor sit amet consectetur adipisicing elit. Ducimus, obcaecati quasi ab inventore odit non vel corrupti? Non unde
        dolore ipsum possimus pariatur repellat corporis impedit vero, quam, facilis nostrum.
      </p>
    </app-card>
    <app-card cardTitle="Body 2">
      <p class="mat-body-2"
        >Lorem ipsum dolor sit amet consectetur, adipisicing elit. Veniam doloremque placeat at ut quis facilis, voluptates aliquam aut
        vitae nihil, commodi quo. Reiciendis rerum dolorem voluptas minus unde voluptatibus vel.</p
      >
      <nav class="m-t-15">
        <ol class="flex align-item-center font-size text-muted">
          <li>
            <h6 class="m-b-0">Size: 14px</h6>
          </li>
          <li class="flex separate">/</li>
          <li>
            <h6 class="m-b-0">Weight: Regular</h6>
          </li>
          <li class="flex separate">/</li>
          <li>
            <h6 class="m-b-0">Line Height: 22px</h6>
          </li>
        </ol>
      </nav>
    </app-card>
    <app-card cardTitle="Subtitle">
      <p class="mat-body-strong"
        >Lorem ipsum dolor sit amet consectetur, adipisicing elit. Veniam doloremque placeat at ut quis facilis, voluptates aliquam aut
        vitae nihil, commodi quo. Reiciendis rerum dolorem voluptas minus unde voluptatibus vel.</p
      >
      <nav class="m-t-15">
        <ol class="flex align-item-center font-size text-muted">
          <li>
            <h6 class="m-b-0">Size: 12px</h6>
          </li>
          <li class="flex separate">/</li>
          <li>
            <h6 class="m-b-0">Weight: Medium</h6>
          </li>
          <li class="flex separate">/</li>
          <li>
            <h6 class="m-b-0">Line Height: 22px</h6>
          </li>
        </ol>
      </nav>
    </app-card>
    <app-card cardTitle="Caption">
      <p class="mat-caption"
        >Lorem ipsum dolor sit amet consectetur, adipisicing elit. Veniam doloremque placeat at ut quis facilis, voluptates aliquam aut
        vitae nihil, commodi quo. Reiciendis rerum dolorem voluptas minus unde voluptatibus vel.</p
      >
      <nav class="m-t-15">
        <ol class="flex align-item-center font-size text-muted">
          <li>
            <h6 class="m-b-0">Size: 12px</h6>
          </li>
          <li class="flex separate">/</li>
          <li>
            <h6 class="m-b-0">Weight: Regular</h6>
          </li>
          <li class="flex separate">/</li>
          <li>
            <h6 class="m-b-0">Line Height: 22px</h6>
          </li>
        </ol>
      </nav>
    </app-card>
  </div>
</div>
