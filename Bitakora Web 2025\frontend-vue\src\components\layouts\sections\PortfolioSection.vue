<template>
  <section class="w-full py-12 md:py-24 lg:py-32 bg-white">
    <div class="container mx-auto px-4 md:px-6">
      <!-- Header -->
      <div class="text-center mb-16">
        <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl mb-4 text-gray-900">Mi Portafolio</h2>
        <p class="text-gray-600 text-lg max-w-2xl mx-auto">
          Explora mi trabajo en desarrollo web, diseño gráfico y producción de video reels
        </p>
      </div>

      <!-- Portfolio Categories -->
      <div v-for="category in categories" :key="category" class="mb-20">
        <!-- Category Header -->
        <div class="flex items-center justify-between mb-8">
          <div>
            <h3 class="text-2xl font-bold mb-2 text-gray-900">{{ category }}</h3>
            <p class="text-gray-600">
              {{ portfolioData[category].length }} proyectos • Página {{ currentPages[category] + 1 }} de {{ getTotalPages(category) }}
            </p>
          </div>

          <!-- Navigation Controls -->
          <div v-if="getTotalPages(category) > 1" class="flex items-center gap-2">
            <button 
              @click="goToPrevPage(category)" 
              class="h-10 w-10 inline-flex items-center justify-center rounded-md border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 transition-colors duration-200"
            >
              <chevron-left-icon class="h-4 w-4" />
            </button>
            <span class="text-sm text-gray-600 px-2">
              {{ currentPages[category] + 1 }} / {{ getTotalPages(category) }}
            </span>
            <button 
              @click="goToNextPage(category)" 
              class="h-10 w-10 inline-flex items-center justify-center rounded-md border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 transition-colors duration-200"
            >
              <chevron-right-icon class="h-4 w-4" />
            </button>
          </div>
        </div>

        <!-- Portfolio Grid -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
          <div
            v-for="item in getCurrentItems(category)"
            :key="item.id"
            class="group overflow-hidden hover:shadow-xl transition-all duration-300 rounded-lg border border-gray-200 bg-white shadow-sm max-w-sm mx-auto w-full"
          >
            <div class="relative overflow-hidden">
              <img
                :src="item.image"
                :alt="item.title"
                class="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
              />
              <div class="absolute inset-0 bg-black bg-opacity-60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                <play-icon v-if="category === 'Video Reels'" class="h-12 w-12 text-white" />
                <external-link-icon v-else class="h-8 w-8 text-white" />
              </div>
              <span 
                v-if="category === 'Video Reels' && item.duration" 
                class="absolute bottom-2 right-2 bg-black bg-opacity-80 text-white inline-flex items-center rounded-full border border-gray-700 px-2.5 py-0.5 text-xs font-semibold"
              >
                {{ item.duration }}
              </span>
            </div>
            <div class="p-4">
              <h3 class="font-semibold text-lg mb-2 text-gray-900 truncate">{{ item.title }}</h3>
              <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ item.description }}</p>
              <div v-if="category === 'Desarrollo Web' && item.tech" class="flex flex-wrap gap-1">
                <span 
                  v-for="tech in item.tech.slice(0, 3)" 
                  :key="tech" 
                  class="inline-flex items-center rounded-full border border-gray-300 bg-gray-100 text-gray-800 px-2.5 py-0.5 text-xs font-semibold"
                >
                  {{ tech }}
                </span>
              </div>
              <span 
                v-if="category === 'Diseño Gráfico' && item.category" 
                class="inline-flex items-center rounded-full border border-gray-300 bg-white text-gray-700 px-2.5 py-0.5 text-xs font-semibold"
              >
                {{ item.category }}
              </span>
            </div>
          </div>
        </div>

        <!-- Mobile Navigation -->
        <div v-if="getTotalPages(category) > 1" class="flex justify-center mt-8 md:hidden">
          <div class="flex items-center gap-2">
            <button 
              @click="goToPrevPage(category)" 
              class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors duration-200 border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-9 px-3"
            >
              <chevron-left-icon class="h-4 w-4 mr-1" />
              Anterior
            </button>
            <span class="text-sm text-gray-600 px-4">
              {{ currentPages[category] + 1 }} de {{ getTotalPages(category) }}
            </span>
            <button 
              @click="goToNextPage(category)" 
              class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors duration-200 border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-9 px-3"
            >
              Siguiente
              <chevron-right-icon class="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ChevronLeft as ChevronLeftIcon, ChevronRight as ChevronRightIcon, ExternalLink as ExternalLinkIcon, Play as PlayIcon } from 'lucide-vue-next'

const ITEMS_PER_PAGE = 9

// Datos de ejemplo para cada categoría
const portfolioData = {
  "Desarrollo Web": [
    {
      id: 1,
      title: "E-commerce Platform",
      description: "Plataforma completa de comercio electrónico con carrito de compras, pagos integrados y panel de administración",
      tech: ["React", "Node.js", "MongoDB"],
      image: "/img/portfolio/nn-web1.jpg",
    },
    {
      id: 2,
      title: "Dashboard Analytics",
      description: "Panel de control con métricas en tiempo real y visualización de datos interactiva",
      tech: ["Next.js", "TypeScript", "PostgreSQL"],
      image: "/img/portfolio/nn-web2.jpg",
    },
    {
      id: 3,
      title: "Social Media App",
      description: "Aplicación de redes sociales con chat en tiempo real y compartir contenido multimedia",
      tech: ["React Native", "Firebase", "Redux"],
      image: "/img/portfolio/nn-web3.jpg",
    },
    {
      id: 4,
      title: "CRM System",
      description: "Sistema de gestión de clientes con seguimiento de ventas y automatización de marketing",
      tech: ["Vue.js", "Laravel", "MySQL"],
      image: "/img/portfolio/app-1.jpg",
    },
    {
      id: 5,
      title: "Learning Platform",
      description: "Plataforma de educación online con cursos interactivos y sistema de evaluación",
      tech: ["React", "Express", "MongoDB"],
      image: "/img/portfolio/app-2.jpg",
    },
    {
      id: 6,
      title: "Task Manager",
      description: "Aplicación de gestión de tareas con colaboración en equipo y seguimiento de proyectos",
      tech: ["Angular", "NestJS", "PostgreSQL"],
      image: "/img/portfolio/app-3.jpg",
    },
    {
      id: 7,
      title: "Real Estate Portal",
      description: "Portal inmobiliario con búsqueda avanzada, mapas interactivos y tours virtuales",
      tech: ["Next.js", "Prisma", "Supabase"],
      image: "/img/portfolio/pav-1.jpg",
    },
    {
      id: 8,
      title: "Food Delivery App",
      description: "App de delivery de comida con seguimiento en tiempo real y múltiples métodos de pago",
      tech: ["React Native", "Node.js", "Redis"],
      image: "/img/portfolio/pav-2.jpg",
    },
    {
      id: 9,
      title: "Booking System",
      description: "Sistema de reservas online para hoteles con calendario dinámico y gestión de disponibilidad",
      tech: ["Vue.js", "Django", "PostgreSQL"],
      image: "/img/portfolio/pav-3.jpg",
    },
    {
      id: 10,
      title: "Chat Application",
      description: "Aplicación de mensajería en tiempo real con videollamadas y compartir archivos",
      tech: ["React", "Socket.io", "MongoDB"],
      image: "/img/portfolio/product-1.jpg",
    },
    {
      id: 11,
      title: "Portfolio Website",
      description: "Sitio web de portafolio personal con animaciones y diseño responsive",
      tech: ["Gatsby", "GraphQL", "Netlify"],
      image: "/img/portfolio/product-2.jpg",
    },
    {
      id: 12,
      title: "Blog Platform",
      description: "Plataforma de blogging con editor rich text y sistema de comentarios",
      tech: ["Next.js", "Markdown", "Vercel"],
      image: "/img/portfolio/product-3.jpg",
    },
  ],
  "Diseño Gráfico": [
    {
      id: 1,
      title: "Brand Identity - TechCorp",
      description: "Identidad visual completa para startup tecnológica incluyendo logo, paleta de colores y tipografía",
      category: "Branding",
      image: "/img/portfolio/branding-1.jpg",
    },
    {
      id: 2,
      title: "Packaging Design - Café Artesanal",
      description: "Diseño de empaque para línea de café premium con enfoque en sostenibilidad",
      category: "Packaging",
      image: "/img/portfolio/branding-2.jpg",
    },
    {
      id: 3,
      title: "Editorial - Revista Moderna",
      description: "Diseño editorial para revista de lifestyle con layout moderno y fotografía de alta calidad",
      category: "Editorial",
      image: "/img/portfolio/branding-3.jpg",
    },
    {
      id: 4,
      title: "Logo Design - RestaurantePlus",
      description: "Logotipo para cadena de restaurantes con concepto minimalista y versátil",
      category: "Logo",
      image: "/img/portfolio/logo-logo1.jpg",
    },
    {
      id: 5,
      title: "Poster Series - Festival Musical",
      description: "Serie de posters para evento musical con estilo vibrante y tipografía experimental",
      category: "Poster",
      image: "/img/portfolio/logo-logo2.jpg",
    },
    {
      id: 6,
      title: "Web Design - Agency Portfolio",
      description: "Diseño web para agencia creativa con animaciones interactivas y UX optimizada",
      category: "Web Design",
      image: "/img/portfolio/logo-logo3.jpg",
    },
    {
      id: 7,
      title: "Business Cards - Law Firm",
      description: "Tarjetas de presentación para bufete de abogados con acabados premium",
      category: "Print",
      image: "/img/portfolio/books-1.jpg",
    },
    {
      id: 8,
      title: "Social Media Kit - Fashion Brand",
      description: "Kit completo de redes sociales para marca de moda con templates y guidelines",
      category: "Social Media",
      image: "/img/portfolio/books-2.jpg",
    },
    {
      id: 9,
      title: "Infographic - Data Visualization",
      description: "Infografía de visualización de datos complejos con diseño claro y atractivo",
      category: "Infographic",
      image: "/img/portfolio/books-3.jpg",
    },
    {
      id: 10,
      title: "App UI Design - Fitness Tracker",
      description: "Diseño de interfaz para app de fitness con enfoque en usabilidad y motivación",
      category: "UI Design",
      image: "/img/portfolio/prh-post1.jpg",
    },
    {
      id: 11,
      title: "Brochure - Real Estate",
      description: "Brochure para empresa inmobiliaria con fotografía arquitectónica y layout elegante",
      category: "Print",
      image: "/img/portfolio/prh-post2.jpg",
    },
    {
      id: 12,
      title: "Icon Set - Productivity Tools",
      description: "Set de iconos para herramientas de productividad con estilo consistente y escalable",
      category: "Icons",
      image: "/img/portfolio/prh-post3.jpg",
    },
  ],
  "Video Reels": [
    {
      id: 1,
      title: "Product Launch - Smartphone",
      description: "Reel promocional para lanzamiento de producto con animaciones dinámicas y música envolvente",
      duration: "0:30",
      image: "/img/portfolio/nn-web1.jpg",
    },
    {
      id: 2,
      title: "Behind the Scenes - Photoshoot",
      description: "BTS de sesión fotográfica de moda mostrando el proceso creativo y el equipo",
      duration: "0:45",
      image: "/img/portfolio/nn-web2.jpg",
    },
    {
      id: 3,
      title: "Recipe Tutorial - Pasta Italiana",
      description: "Tutorial rápido de cocina italiana con técnicas profesionales y presentación atractiva",
      duration: "1:00",
      image: "/img/portfolio/nn-web3.jpg",
    },
    {
      id: 4,
      title: "Workout Routine - HIIT",
      description: "Rutina de ejercicios de alta intensidad con instrucciones claras y motivación",
      duration: "0:60",
      image: "/img/portfolio/branding-1.jpg",
    },
    {
      id: 5,
      title: "Travel Vlog - Bali Adventure",
      description: "Aventura de viaje por Bali capturando paisajes increíbles y experiencias únicas",
      duration: "1:30",
      image: "/img/portfolio/branding-2.jpg",
    },
    {
      id: 6,
      title: "Tech Review - Latest Gadgets",
      description: "Review detallado de los últimos gadgets tecnológicos con análisis profesional",
      duration: "2:00",
      image: "/img/portfolio/branding-3.jpg",
    },
    {
      id: 7,
      title: "Fashion Haul - Spring Collection",
      description: "Haul de la nueva colección primaveral con styling tips y combinaciones",
      duration: "1:15",
      image: "/img/portfolio/app-1.jpg",
    },
    {
      id: 8,
      title: "DIY Project - Home Decor",
      description: "Proyecto DIY para decoración del hogar con materiales accesibles y resultados profesionales",
      duration: "0:50",
      image: "/img/portfolio/app-2.jpg",
    },
    {
      id: 9,
      title: "Music Performance - Acoustic Session",
      description: "Sesión acústica en vivo con interpretación emotiva y calidad de audio profesional",
      duration: "3:00",
      image: "/img/portfolio/app-3.jpg",
    },
    {
      id: 10,
      title: "Business Tips - Entrepreneurship",
      description: "Consejos prácticos para emprendedores basados en experiencia real y casos de éxito",
      duration: "1:45",
      image: "/img/portfolio/pav-1.jpg",
    },
    {
      id: 11,
      title: "Art Process - Digital Painting",
      description: "Proceso completo de creación de arte digital desde boceto hasta obra final",
      duration: "2:30",
      image: "/img/portfolio/pav-2.jpg",
    },
    {
      id: 12,
      title: "Event Highlights - Conference 2024",
      description: "Highlights de conferencia tecnológica con momentos clave y networking",
      duration: "1:20",
      image: "/img/portfolio/pav-3.jpg",
    },
  ],
}

const categories = Object.keys(portfolioData)

const currentPages = ref({
  "Desarrollo Web": 0,
  "Diseño Gráfico": 0,
  "Video Reels": 0,
})

const getCurrentItems = (category) => {
  const startIndex = currentPages.value[category] * ITEMS_PER_PAGE
  return portfolioData[category].slice(startIndex, startIndex + ITEMS_PER_PAGE)
}

const getTotalPages = (category) => {
  return Math.ceil(portfolioData[category].length / ITEMS_PER_PAGE)
}

const goToNextPage = (category) => {
  const totalPages = getTotalPages(category)
  currentPages.value[category] = (currentPages.value[category] + 1) % totalPages
}

const goToPrevPage = (category) => {
  const totalPages = getTotalPages(category)
  currentPages.value[category] = currentPages.value[category] === 0 ? totalPages - 1 : currentPages.value[category] - 1
}
</script>

<style scoped>
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2; /* Propiedad estándar para compatibilidad */
}
</style>