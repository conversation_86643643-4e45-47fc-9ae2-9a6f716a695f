name: <PERSON>tra<PERSON> deploy

# Controls when the action will run.
on:
  # Triggers the workflow on push or pull request events but only for the master branch
  push:
    branches:
      - master
    paths:
      - 'bootstrap/**'
  #pull_request:
  #  types:
  #    - closed
  #  branches:
  #    - master

jobs:
  #if_merged:
  #  if: github.event.pull_request.merged == true
  Bootstrap-Deploy:
    name: 🎉 Bootstrap Deploy
    runs-on: ubuntu-latest

    steps:
      - name: 🚚 Get latest code
        uses: actions/checkout@v3

      - name: 📂 Deploy to Server
        uses: easingthemes/ssh-deploy@main
        env:
          SSH_PRIVATE_KEY: ${{ secrets.SERVER_SSH_KEY }}
          # ARGS: "-rltgoDzvO --delete"
          SOURCE: "bootstrap/dist/"
          REMOTE_HOST: *************
          REMOTE_USER: ableproadmin
          TARGET: public_html/bootstrap/free
          EXCLUDE: "/bootstrap/node_modules/"
