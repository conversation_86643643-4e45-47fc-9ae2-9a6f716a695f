name: <PERSON>act deploy

# Controls when the action will run.
on:
  # Triggers the workflow on push or pull request events but only for the master branch
  push:
    branches:
      - master
    paths:
      - 'react/**'
  #pull_request:
  #  types:
  #    - closed
  #  branches:
  #    - master

jobs:
  #if_merged:
  #  if: github.event.pull_request.merged == true
  React-Deploy:
    name: 🎉 React Deploy
    runs-on: ubuntu-latest

    steps:
      - name: 🚚 Get latest code
        uses: actions/checkout@v4

      - name: Install Node.js 20
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"

      - name: 🔨 Build Project
        run: |
          cd react
          yarn install
          yarn run build

      - name: 📂 Deploy to Server
        uses: easingthemes/ssh-deploy@main
        env:
          SSH_PRIVATE_KEY: ${{ secrets.SERVER_SSH_KEY }}
          # ARGS: "-rltgoDzvO --delete"
          SOURCE: "react/dist/"
          REMOTE_HOST: *************
          REMOTE_USER: ableproadmin
          TARGET: public_html/react/free
          EXCLUDE: "/react/node_modules/"
