{"name": "able-pro-react-free-admin-dashboard", "version": "1.3.0", "private": true, "dependencies": {"@date-io/date-fns": "^3.0.0", "@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/base": "^5.0.0-beta.40", "@mui/lab": "^5.0.0-alpha.170", "@mui/material": "^5.15.15", "@mui/system": "^5.15.15", "@reduxjs/toolkit": "^2.2.3", "@svgr/webpack": "^8.1.0", "@vitejs/plugin-react": "^4.2.1", "apexcharts": "^3.45.2", "axios": "^1.6.8", "crypto-js": "^4.2.0", "date-fns": "^3.3.1", "formik": "^2.4.5", "framer-motion": "^11.0.25", "history": "^5.3.0", "iconsax-react": "^0.0.8", "lodash": "^4.17.21", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-device-detect": "^2.2.3", "react-dom": "^18.2.0", "react-redux": "^9.1.2", "react-router": "^6.21.3", "react-router-dom": "^6.21.3", "simplebar": "^5.3.9", "simplebar-react": "^2.4.1", "slick-carousel": "^1.8.1", "stream-browserify": "^3.0.0", "util": "^0.12.5", "uuid": "^9.0.1", "vite": "^5.2.10", "vite-jsconfig-paths": "^2.0.1", "web-vitals": "^3.5.2", "yup": "^1.4.0"}, "scripts": {"start": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "prettier": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "babel": {"presets": ["@babel/preset-react"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.24.4", "@babel/eslint-parser": "^7.24.1", "@babel/plugin-proposal-private-property-in-object": "7.21.11", "env-cmd": "^10.1.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "immutable": "^4.3.5", "prettier": "^3.2.4", "prettier-eslint-cli": "^8.0.1", "react-error-overlay": "^6.0.11"}}