# Able Pro Free Angular Material UI Dashboard Template [![Tweet](https://img.shields.io/twitter/url/http/shields.io.svg?style=social)](https://twitter.com/intent/tweet?text=Get%20Able%20%20Pro%20Angular%20-%20The%20most%20Beautiful%20Material%20Designed%20Admin%20Dashboard%20Template%20&url=https://ableproadmin.com/angular/default/&via=phoenixcoded&hashtags=angular,webdev,developers,javascript)

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Price](https://img.shields.io/badge/price-FREE-0098f7.svg)](https://github.com/codedthemes/able-pro-free-admin-dashboard-template/blob/master/LICENSE)

Able Pro is a free Material admin dashboard template built with Angular. It is designed to offer an exceptional User Experience, with a wide range of customizable and feature-rich pages. Able Pro serves as a comprehensive Dashboard Template, boasting a user-friendly and responsive design that adapts seamlessly to retina screens as well as laptops.

:star: :star: :star: Do not forget to star (Top right of this page) it if you like the Able Pro :star: :star: :star:

![IMG_8566.jpg](https://org-public-assets.s3.us-west-2.amazonaws.com/Free-Version-Banners/GITHUB-FREE-ANGULAR-REPO%20-%20Able%20pro%20admin.jpg)

Looking for even more features? Check out Able Pro's Angular [premium version](https://1.envato.market/zNkqj6), which offers an even wider array of pages, features, and layouts than the free version, providing you with the ultimate in customization and control.

| [Able-pro Free](https://ableproadmin.com/angular/free) | [Able-Pro Pro Version](https://1.envato.market/zNkqj6)      |
| ------------------------------------------------------ | :---------------------------------------------------------- |
| **7** Demo pages                                       | **100+** pages                                              |
| -                                                      | ✓ Modern UserInterface & Easy Developer Experience          |
| -                                                      | ✓ Dark/Light 🌓                                             |
| -                                                      | ✓ Material Components                                       |
| -                                                      | ✓ Design files [Figma](https://links.codedthemes.com/mQZrX) |
| -                                                      | ✓ 10+ color Presets                                         |
| -                                                      | ✓ LTR/RTL Layouts                                           |
| -                                                      | ✓ Vertical/Horizontal/Compact/Tab etc...Layouts             |
| -                                                      | ✓ Advance Components                                        |
| -                                                      | ✓ Form Variants                                             |
| -                                                      | ✓ Table Variants                                            |
| -                                                      | ✓ 7+ Conceptual Apps                                        |
| -                                                      | ✓ Front Pages                                               |

## About Able-pro Angular Admin Dashboard Template?

Welcome to Able Pro, the ultimate free Angular Dashboard Template powered by the Material-UI components library. With its modern design interface and flexible developer experience, Able Pro is the perfect solution for creating stunning and feature-rich admin dashboards. Whether you're a beginner or an experienced developer, this template offers a seamless and intuitive workflow, allowing you to easily customize and build beautiful user interfaces. Experience the power of Able Pro and elevate your web application to new heights.

- Modern UI design
- Angular Material component
- Fully Responsive, all modern browser supported
- Simple code structure
- Flexible & High-Performance code
- Easy Documentation Guide

## Able Pro Free version

#### Preview

- [Preview](https://ableproadmin.com/angular/free)

#### Download

- [Download from GitHub](https://github.com/phoenixcoded/able-pro-free-admin-dashboard-template)

## Able Pro Premium version

#### Preview

- [Preview](https://ableproadmin.com/angular/default/)

#### Purchase

- [Buy Now](https://1.envato.market/zNkqj6)

## Table of contents

- [Getting Started](#getting-started)
- [Documentation](#documentation)
- [Technology Stack](#technology-stack)
- [Author](#author)
- [Issues?](#issues)
- [License](#license)
- [More Free Angular Templates](#more-free-angular-dashboard-templates)
- [More Pro Angular Templates](#more-premium-angular-dashboard-templates)
- [Follow us](#follow-us)

## Getting Started

1. Clone from Github

```
git clone https://github.com/phoenixcoded/able-pro-free-admin-dashboard-template.git
```

2. Install packages

```
yarn
```

3. Run project

```
yarn start
```

## Documentation

[Able Pro Angular Documentation](https://phoenixcoded.gitbook.io/able-pro/v/angular) helps you with installation, deployment, and troubleshooting.

## Technology Stack

- Angular 19.x.x
- Angular Material
- TypeScript
- JWT (i.e. Pro version)
- Role Base Authentication (i.e. Pro version)
- Apex Charts
- npm package installer

## Author

Able-pro is developed by Team [Phoenixcoded](https://phoenixcoded.net/).

## Issues

To report a bug, please submit an [issue](https://github.com/codedthemes/able-pro-free-admin-dashboard-template/issues) on Github. We will respond as soon as possible to resolve the issue.

## License

- Licensed cover under [MIT](https://github.com/phoenixcoded/able-pro-free-admin-dashboard-template/blob/master/LICENSE)

## Able Pro is Available in Following Technologies

- [Bootstrap 5](https://themeforest.net/item/able-pro-bootstrap-admin-dashboard-template/50170229)
- [React MUI (Material-UI)](https://themeforest.net/item/able-pro-react-nextjs-admin-dashboard/50613770)
- [CodeIgniter](https://themeforest.net/item/able-pro-responsive-bootstrap-4-admin-template/19300403)
- [Asp.net](https://themeforest.net/item/able-pro-responsive-bootstrap-4-admin-template/19300403)
- [Angular](https://themeforest.net/item/able-pro-angular-dashboard-template/50607360)
- [VueJs](https://themeforest.net/item/able-pro-responsive-bootstrap-4-admin-template/19300403)
- [NextJS](https://themeforest.net/item/able-pro-react-nextjs-admin-dashboard/50613770)

## More Free Angular Dashboard Templates

- [Mantis Angular](https://codedthemes.com/item/mantis-angular-free-admin-template/)
- [Datta Able Angular](https://codedthemes.com/item/datta-able-angular-lite/)
- [Next Angular](https://codedthemes.com/item/next-free-admin-template/)
- [Berry Angular](https://codedthemes.com/item/berry-angular-free-admin-template/)

## More Premium Angular Dashboard Templates

- [mantis Angular Pro](https://codedthemes.com/item/mantis-angular-admin-template/)
- [Datta Able Angular Pro](https://codedthemes.com/item/datta-able-angular/)
- [Next Angular Pro](https://codedthemes.com/item/next-angular-admin-template/)
- [Berry Angular Pro](https://codedthemes.com/item/berry-angular-admin-dashboard-template/)

## Follow us

- [Website](https://ableproadmin.com/)
- [Phoenixcoded](https://themeforest.net/user/phoenixcoded)
- [Dribbble](https://dribbble.com/codedthemes)
- [Facebook](https://www.facebook.com/codedthemes)
- [Twitter](https://twitter.com/codedthemes)
