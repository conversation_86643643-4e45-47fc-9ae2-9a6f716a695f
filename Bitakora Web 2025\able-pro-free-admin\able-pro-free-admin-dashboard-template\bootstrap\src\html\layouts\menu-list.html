<li class="pc-item pc-caption">
  <label>Navigation</label>
</li>

<li class="pc-item">
  <a href="../dashboard/index.html" class="pc-link">
    <span class="pc-micon">
      <svg class="pc-icon">
        <use xlink:href="#custom-status-up"></use>
      </svg>
    </span>
    <span class="pc-mtext">Dashboard</span>
  </a>
</li>

<li class="pc-item pc-caption">
  <label>Authentication</label>
</li>
<li class="pc-item">
  <a href="../pages/login-v1.html" class="pc-link" target="_blank"
    ><span class="pc-micon">
      <svg class="pc-icon">
        <use xlink:href="#custom-shield"></use>
      </svg> </span
    ><span class="pc-mtext">Login</span></a
  >
</li>
<li class="pc-item">
  <a href="../pages/register-v1.html" class="pc-link" target="_blank"
    ><span class="pc-micon">
      <svg class="pc-icon">
        <use xlink:href="#custom-password-check"></use>
      </svg> </span
    ><span class="pc-mtext">Register</span></a
  >
</li>
<li class="pc-item pc-caption">
  <label>UI Components</label>
  <svg class="pc-icon">
    <use xlink:href="#custom-box-1"></use>
  </svg>
</li>
<li class="pc-item">
  <a href="../elements/bc_typography.html" class="pc-link"
    ><span class="pc-micon">
      <svg class="pc-icon">
        <use xlink:href="#custom-text-block"></use>
      </svg> </span
    ><span class="pc-mtext">Typography</span></a
  >
</li>
<li class="pc-item">
  <a href="../elements/bc_color.html" class="pc-link"
    ><span class="pc-micon">
      <svg class="pc-icon">
        <use xlink:href="#custom-clipboard"></use>
      </svg> </span
    ><span class="pc-mtext">Color</span></a
  >
</li>
<li class="pc-item">
  <a href="../elements/icon-tabler.html" class="pc-link"
    ><span class="pc-micon">
      <svg class="pc-icon">
        <use xlink:href="#custom-mouse-circle"></use>
      </svg> </span
    ><span class="pc-mtext">Tabler</span>
  </a>
</li>
<li class="pc-item pc-caption">
  <label>Other</label>
  <svg class="pc-icon">
    <use xlink:href="#custom-notification-status"></use>
  </svg>
</li>
<li class="pc-item pc-hasmenu">
  <a href="#!" class="pc-link"
    ><span class="pc-micon">
      <svg class="pc-icon">
        <use xlink:href="#custom-level"></use>
      </svg> </span
    ><span class="pc-mtext">Menu levels</span><span class="pc-arrow"><i data-feather="chevron-right"></i></span
  ></a>
  <ul class="pc-submenu">
    <li class="pc-item"><a class="pc-link" href="#!">Level 2.1</a></li>
    <li class="pc-item pc-hasmenu">
      <a href="#!" class="pc-link"
        >Level 2.2<span class="pc-arrow"><i data-feather="chevron-right"></i></span
      ></a>
      <ul class="pc-submenu">
        <li class="pc-item"><a class="pc-link" href="#!">Level 3.1</a></li>
        <li class="pc-item"><a class="pc-link" href="#!">Level 3.2</a></li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link"
            >Level 3.3<span class="pc-arrow"><i data-feather="chevron-right"></i></span
          ></a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="#!">Level 4.1</a></li>
            <li class="pc-item"><a class="pc-link" href="#!">Level 4.2</a></li>
          </ul>
        </li>
      </ul>
    </li>
    <li class="pc-item pc-hasmenu">
      <a href="#!" class="pc-link"
        >Level 2.3<span class="pc-arrow"><i data-feather="chevron-right"></i></span
      ></a>
      <ul class="pc-submenu">
        <li class="pc-item"><a class="pc-link" href="#!">Level 3.1</a></li>
        <li class="pc-item"><a class="pc-link" href="#!">Level 3.2</a></li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link"
            >Level 3.3<span class="pc-arrow"><i data-feather="chevron-right"></i></span
          ></a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="#!">Level 4.1</a></li>
            <li class="pc-item"><a class="pc-link" href="#!">Level 4.2</a></li>
          </ul>
        </li>
      </ul>
    </li>
  </ul>
</li>
<li class="pc-item"
  ><a href="../other/sample-page.html" class="pc-link">
    <span class="pc-micon">
      <svg class="pc-icon">
        <use xlink:href="#custom-notification-status"></use>
      </svg>
    </span>
    <span class="pc-mtext">Sample page</span></a
  ></li
>
