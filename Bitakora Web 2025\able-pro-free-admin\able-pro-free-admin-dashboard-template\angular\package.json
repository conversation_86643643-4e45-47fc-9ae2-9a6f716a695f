{"name": "able-pro-angular-free", "version": "2.5.0", "author": "PhoenixCoded", "license": "MIT", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration production --base-href /angular/free/", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint", "lint:fix": "ng lint --fix", "prettier": "prettier --write ./src"}, "dependencies": {"@angular/animations": "19.0.5", "@angular/cdk": "19.0.4", "@angular/common": "19.0.5", "@angular/compiler": "19.0.5", "@angular/core": "19.0.5", "@angular/forms": "19.0.5", "@angular/material": "19.0.4", "@angular/platform-browser": "19.0.5", "@angular/platform-browser-dynamic": "19.0.5", "@angular/router": "19.0.5", "apexcharts": "3.49.0", "ng-apexcharts": "1.10.0", "ngx-scrollbar": "16.1.1", "rxjs": "~7.8.1", "tslib": "2.8.1", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "19.0.6", "@angular-eslint/builder": "19.0.2", "@angular-eslint/eslint-plugin": "19.0.2", "@angular-eslint/eslint-plugin-template": "19.0.2", "@angular-eslint/schematics": "19.0.2", "@angular-eslint/template-parser": "19.0.2", "@angular/cli": "~19.0.6", "@angular/compiler-cli": "19.0.5", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.17.0", "@types/jasmine": "~5.1.5", "@typescript-eslint/eslint-plugin": "8.18.1", "@typescript-eslint/parser": "8.18.1", "eslint": "9.17.0", "jasmine-core": "~5.5.0", "karma": "~6.4.4", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.1", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "prettier": "3.4.2", "typescript": "5.6.3"}}