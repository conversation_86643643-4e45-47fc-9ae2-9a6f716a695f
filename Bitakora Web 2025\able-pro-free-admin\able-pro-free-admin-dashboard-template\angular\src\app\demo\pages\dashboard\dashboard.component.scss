@import '../../../@theme/styles/theme-palette-colors.scss';

.able-user-group .avatar {
  width: 30px;
  height: 30px;
}

.chart-body {
  margin-top: 16px;
  padding: 16px;
  border-radius: 8px;
  background: var(--accent-100);
}

.border {
  border: 1px solid var(--accent-300);
}

.border-50 {
  border-radius: 50%;
}

.user-status {
  font-size: 0.75em !important;
  padding: 0.25em 0.9em;
  display: inline-block;
  border-radius: 8px;
}

.card-head {
  margin: 0px 10px;
}

.income-value {
  background: var(--accent-100);
  padding: 16px;
  border-radius: 8px;
}

.gap {
  gap: 1rem;

  .dot {
    border-radius: 50%;
    background: map-get($yellow-theme, 500);

    &.primary {
      background: var(--primary-500);
    }
  }
}
